using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Application.Constants;
using HappyWechat.Infrastructure.AccountConfig;
using HappyWechat.Infrastructure.Configuration;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.MessageQueue.Redis;
using HappyWechat.Application.Interfaces;
using AccountConfigChecker = HappyWechat.Infrastructure.AccountConfig.IAccountConfigChecker;
using HappyWechat.Infrastructure.Caching;
using HappyWechat.Infrastructure.Caching.Interfaces;
// using HappyWechat.Infrastructure.MessageProcessing.Processors; // 已移除，使用Services
using HappyWechat.Infrastructure.Monitoring;
using HappyWechat.Infrastructure.Monitoring.Services;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Monitoring;
using HappyWechat.Infrastructure.MessageQueue.Core;
using HappyWechat.Infrastructure.Configuration.Models;
using HappyWechat.Infrastructure.EYun;
using HappyWechat.Application.DTOs.Wrappers.EYun.Requests.Friends;
using RiskControlSensitiveWordService = HappyWechat.Infrastructure.RiskControl.Services.ISensitiveWordDetectionService;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// EYun回调处理器接口
/// </summary>
public interface IEYunCallbackProcessor
{
    /// <summary>
    /// 处理EYun回调消息
    /// </summary>
    Task<EYunCallbackProcessResult> ProcessCallbackAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default);
}

/// <summary>
/// EYun回调处理结果
/// </summary>
public class EYunCallbackProcessResult
{
    public bool Success { get; set; }
    public bool ShouldContinue { get; set; } = true;
    public string? ErrorMessage { get; set; }
    public string? ProcessingDetails { get; set; }
    public EYunCallbackProcessStage CompletedStage { get; set; }
    public Dictionary<string, object> ExtendedData { get; set; } = new();

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static EYunCallbackProcessResult CreateSuccess(string details, EYunCallbackProcessStage stage)
    {
        return new EYunCallbackProcessResult
        {
            Success = true,
            ShouldContinue = true,
            ProcessingDetails = details,
            CompletedStage = stage
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static EYunCallbackProcessResult CreateFailure(string errorMessage, EYunCallbackProcessStage stage, Dictionary<string, object>? extendedData = null)
    {
        return new EYunCallbackProcessResult
        {
            Success = false,
            ShouldContinue = false,
            ErrorMessage = errorMessage,
            CompletedStage = stage,
            ExtendedData = extendedData ?? new Dictionary<string, object>()
        };
    }
}

/// <summary>
/// EYun回调处理阶段
/// </summary>
public enum EYunCallbackProcessStage
{
    Validation,
    AccountConfigCheck,
    GlobalConfigLoad,
    EntityValidation,  // 🔧 新增：实体存在性验证阶段
    MessageTypeClassification,
    FriendRequestProcessing,
    GroupInviteProcessing,
    MessageProcessing,
    QueueRouting,
    Completed
}

/// <summary>
/// EYun回调处理器实现 - 按照流程图实现完整的处理逻辑
/// </summary>
public class EYunCallbackProcessor : IEYunCallbackProcessor, IConfigurationChangeListener
{
    private readonly AccountConfigChecker _accountConfigChecker;
    private readonly IUnifiedConfigurationManager _configManager;
    private readonly RiskControlSensitiveWordService _sensitiveWordService;
    private readonly IAutoAcceptFriendService _autoAcceptFriendService;
    private readonly IFriendRequestMessageParser _friendRequestParser;
    private readonly IEYunFriendRequestWrapper _eYunFriendRequestWrapper;
    private readonly IMessageTypeClassifier _messageTypeClassifier;

    // 配置缓存
    private EYunRiskControlConfigDto? _cachedConfig;
    private readonly object _configLock = new();

    private readonly IAtUserDetectionService _atUserDetectionService;
    private readonly IRedisMessageRouter _messageRouter;
    private readonly IWxManagerIdCacheService _wxManagerIdCacheService;
    private readonly IMixedContentParser _mixedContentParser;
    private readonly IMediaMessageProcessor _mediaMessageProcessor;
    private readonly IMessageConfigValidator _messageConfigValidator;
    private readonly IOfflineNotificationProcessor _offlineNotificationProcessor;
    private readonly IFriendRequestProcessor _friendRequestProcessor;
    // 群邀请处理器已被删除
    // private readonly IGroupInviteProcessor _groupInviteProcessor;
    private readonly IEnhancedSensitiveWordService _enhancedSensitiveWordService;
    private readonly IMessageProcessingRetryService _retryService;
    private readonly IMessageProcessingMetrics _metrics;
    private readonly IMessageProcessingMonitorService? _messageProcessingMonitor;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMessageDeduplicationService _messageDeduplicationService;
    private readonly IContactNicknameService _contactNicknameService;
    private readonly IUnifiedCacheManager _cacheManager;
    private readonly ILogger<EYunCallbackProcessor> _logger;

    public EYunCallbackProcessor(
        AccountConfigChecker accountConfigChecker,
        IUnifiedConfigurationManager configManager,
        RiskControlSensitiveWordService sensitiveWordService,
        IAutoAcceptFriendService autoAcceptFriendService,
        IFriendRequestMessageParser friendRequestParser,
        IEYunFriendRequestWrapper eYunFriendRequestWrapper,
        IMessageTypeClassifier messageTypeClassifier,

        IAtUserDetectionService atUserDetectionService,
        IRedisMessageRouter messageRouter,
        IWxManagerIdCacheService wxManagerIdCacheService,
        IMixedContentParser mixedContentParser,
        IMediaMessageProcessor mediaMessageProcessor,
        IMessageConfigValidator messageConfigValidator,
        IOfflineNotificationProcessor offlineNotificationProcessor,
        IFriendRequestProcessor friendRequestProcessor,
        // IGroupInviteProcessor groupInviteProcessor, // 已删除
        IEnhancedSensitiveWordService enhancedSensitiveWordService,
        IMessageProcessingRetryService retryService,
        IMessageProcessingMetrics metrics,
        IMessageProcessingMonitorService? messageProcessingMonitor,
        ApplicationDbContext dbContext,
        IMessageDeduplicationService messageDeduplicationService,
        IContactNicknameService contactNicknameService,
        IUnifiedCacheManager cacheManager,
        ILogger<EYunCallbackProcessor> logger)
    {
        _accountConfigChecker = accountConfigChecker;
        _configManager = configManager;
        _sensitiveWordService = sensitiveWordService;
        _autoAcceptFriendService = autoAcceptFriendService;
        _friendRequestParser = friendRequestParser;
        _eYunFriendRequestWrapper = eYunFriendRequestWrapper;
        _messageTypeClassifier = messageTypeClassifier;

        _atUserDetectionService = atUserDetectionService;
        _messageRouter = messageRouter;
        _wxManagerIdCacheService = wxManagerIdCacheService;
        _mixedContentParser = mixedContentParser;
        _mediaMessageProcessor = mediaMessageProcessor;
        _messageConfigValidator = messageConfigValidator;
        _offlineNotificationProcessor = offlineNotificationProcessor;
        _friendRequestProcessor = friendRequestProcessor;
        // _groupInviteProcessor = groupInviteProcessor; // 已删除
        _enhancedSensitiveWordService = enhancedSensitiveWordService;
        _retryService = retryService;
        _metrics = metrics;
        _messageProcessingMonitor = messageProcessingMonitor;
        _dbContext = dbContext;
        _messageDeduplicationService = messageDeduplicationService;
        _contactNicknameService = contactNicknameService;
        _cacheManager = cacheManager;
        _logger = logger;
    }

    /// <summary>
    /// 配置变更通知处理
    /// </summary>
    public async Task OnConfigurationChangedAsync(ConfigurationChangeNotificationEvent changeEvent)
    {
        try
        {
            _logger.LogInformation("收到配置变更通知 - ConfigKey: {ConfigKey}, Reason: {Reason}",
                changeEvent.Key, changeEvent.Reason);

            // 清除缓存的配置
            lock (_configLock)
            {
                _cachedConfig = null;
            }

            _logger.LogDebug("已清除缓存的全局配置，下次处理时将重新加载");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理配置变更通知失败 - ConfigKey: {ConfigKey}", changeEvent.Key);
        }
    }

    public async Task<EYunCallbackProcessResult> ProcessCallbackAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default)
    {
        // 开始性能监控
        using var processingTimer = _metrics.StartMessageProcessing(callbackMessage.MessageType!, callbackMessage.WxManagerId ?? "unknown");
        var startTime = DateTime.UtcNow;

        try
        {
            // 🔧 注释原有的开始处理日志，已合并到统一消息接收日志中
            // _logger.LogInformation("[{ProcessingId}] 🚀 开始EYun回调处理 - WcId: {WcId}, MessageType: {MessageType}",
            //     processingId, callbackMessage.WcId, callbackMessage.MessageType);

            // 🔧 修复：阶段0: 消息去重检查（已在IsDuplicateMessageAsync中原子性标记）
            if (await _messageDeduplicationService.IsDuplicateMessageAsync(callbackMessage))
            {
                // 🔧 精简日志 - 注释掉重复消息检测日志，减少日志噪音
                // _logger.LogWarning("[{ProcessingId}] 检测到重复消息，跳过处理 - MsgId: {MsgId}",
                //     processingId, callbackMessage.Data?.MsgId);
                return EYunCallbackProcessResult.CreateSuccess("重复消息已跳过", EYunCallbackProcessStage.Validation);
            }

            // 🔧 修复：消息已在去重检查时标记，这里只需要记录开始处理
            // _logger.LogDebug("[{ProcessingId}] 消息去重检查通过，开始处理 - MsgId: {MsgId}",
            //     processingId, callbackMessage.Data?.MsgId);

            // 阶段1: 基础验证
            var validationResult = await ValidateCallbackMessageAsync(callbackMessage, processingId);
            if (!validationResult.Success)
            {
                return validationResult;
            }

            // 阶段1.5: wcId到WxManagerId转换和设置
            var conversionResult = await ConvertAndSetWxManagerIdAsync(callbackMessage, processingId);
            if (!conversionResult.Success)
            {
                return conversionResult;
            }

            // 阶段2: 账号配置检查
            var accountCheckResult = await CheckAccountConfigAsync(callbackMessage, processingId);
            if (!accountCheckResult.Success || !accountCheckResult.ShouldContinue)
            {
                return accountCheckResult;
            }

            // 阶段2.5: 实体存在性验证 (新增关键步骤)
            var entityValidationResult = await ValidateEntityExistenceAsync(callbackMessage, processingId);
            if (!entityValidationResult.Success || !entityValidationResult.ShouldContinue)
            {
                return entityValidationResult;
            }

            // 阶段3: 记录统一消息接收日志
            if (Guid.TryParse(callbackMessage.WxManagerId, out var wxManagerId))
            {
                await LogUnifiedMessageReceivedAsync(processingId, callbackMessage, wxManagerId, true);
            }

            // 阶段4: 加载全局机器人配置（使用缓存）
            var globalConfig = await GetCachedGlobalConfigAsync(processingId);

            // 阶段5: 消息类型分类处理
            var classificationResult = await ClassifyAndProcessMessageAsync(callbackMessage, globalConfig, processingId, cancellationToken);
            if (!classificationResult.Success || !classificationResult.ShouldContinue)
            {
                return classificationResult;
            }

            // 阶段5: 路由到消息队列
            var routingResult = await RouteToMessageQueueAsync(callbackMessage, processingId, cancellationToken);

            // _logger.LogInformation("[{ProcessingId}] ✅ EYun回调处理完成 - 成功: {Success}",
            //     processingId, routingResult.Success); // 已简化，避免冗余日志

            // 记录处理成功指标
            var duration = DateTime.UtcNow - startTime;
            _metrics.RecordMessageProcessingSuccess(callbackMessage.MessageType!, callbackMessage.WxManagerId!, duration);

            return routingResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ EYun回调处理异常", processingId);

            // 记录处理失败指标
            var duration = DateTime.UtcNow - startTime;
            _metrics.RecordMessageProcessingFailure(callbackMessage.MessageType!, callbackMessage.WxManagerId ?? "unknown", "Exception", duration);

            // 判断是否需要重试
            if (ShouldRetryProcessing(ex))
            {
                _logger.LogWarning("[{ProcessingId}] 消息处理失败，安排重试 - Error: {Error}", processingId, ex.Message);
                await _retryService.RetryMessageProcessingAsync(callbackMessage, ex.Message);
            }

            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"处理异常: {ex.Message}",
                ProcessingDetails = $"ProcessingId: {processingId}, Exception: {ex.GetType().Name}",
                CompletedStage = EYunCallbackProcessStage.Validation
            };
        }
    }

    /// <summary>
    /// 验证回调消息
    /// </summary>
    private async Task<EYunCallbackProcessResult> ValidateCallbackMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        _logger.LogDebug("[{ProcessingId}] 📋 开始基础验证", processingId);

        if (callbackMessage == null)
        {
            _logger.LogWarning("[{ProcessingId}] 回调消息为空", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = "回调消息为空",
                CompletedStage = EYunCallbackProcessStage.Validation
            };
        }

        if (string.IsNullOrEmpty(callbackMessage.WcId))
        {
            _logger.LogWarning("[{ProcessingId}] WcId为空", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = "WcId为空",
                CompletedStage = EYunCallbackProcessStage.Validation
            };
        }

        if (string.IsNullOrEmpty(callbackMessage.MessageType))
        {
            _logger.LogWarning("[{ProcessingId}] MessageType为空", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = "MessageType为空",
                CompletedStage = EYunCallbackProcessStage.Validation
            };
        }

        _logger.LogDebug("[{ProcessingId}] ✅ 基础验证通过", processingId);
        return new EYunCallbackProcessResult
        {
            Success = true,
            ShouldContinue = true,
            CompletedStage = EYunCallbackProcessStage.Validation
        };

        await Task.CompletedTask;
    }

    /// <summary>
    /// 转换wcId到WxManagerId并设置到消息对象
    /// </summary>
    private async Task<EYunCallbackProcessResult> ConvertAndSetWxManagerIdAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        // _logger.LogDebug("[{ProcessingId}] 🔄 开始wcId到WxManagerId转换 - WcId: {WcId}", processingId, callbackMessage.WcId);

        try
        {
            // 优先通过wcId查找
            var wxManagerId = await _wxManagerIdCacheService.GetWxManagerIdAsync(callbackMessage.WcId!);

            // 如果wcId查找失败，尝试通过wId查找（如果消息数据中有wId）
            if (!wxManagerId.HasValue && callbackMessage.Data?.WId != null)
            {
                wxManagerId = await _wxManagerIdCacheService.GetWxManagerIdByWIdAsync(callbackMessage.Data.WId);
                // _logger.LogDebug("[{ProcessingId}] 通过wId查找WxManagerId - WId: {WId}, Result: {WxManagerId}",
                //    processingId, callbackMessage.Data.WId, wxManagerId);
            }

            if (!wxManagerId.HasValue)
            {
                //_logger.LogWarning("[{ProcessingId}] 无法找到对应的WxManagerId - WcId: {WcId}, WId: {WId}",
                //   processingId, callbackMessage.WcId, callbackMessage.Data?.WId);

                return new EYunCallbackProcessResult
                {
                    Success = false,
                    ShouldContinue = false,
                    ErrorMessage = $"无法找到对应的WxManagerId - WcId: {callbackMessage.WcId}",
                    CompletedStage = EYunCallbackProcessStage.Validation
                };
            }

            // 设置WxManagerId到消息对象
            callbackMessage.WxManagerId = wxManagerId.Value.ToString();

            // _logger.LogDebug("[{ProcessingId}] ✅ wcId到WxManagerId转换成功 - WcId: {WcId}, WxManagerId: {WxManagerId}",
            //    processingId, callbackMessage.WcId, wxManagerId.Value);

            return new EYunCallbackProcessResult
            {
                Success = true,
                ShouldContinue = true,
                CompletedStage = EYunCallbackProcessStage.Validation,
                ExtendedData = { ["WxManagerId"] = wxManagerId.Value }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] wcId到WxManagerId转换异常 - WcId: {WcId}", processingId, callbackMessage.WcId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"wcId到WxManagerId转换异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.Validation
            };
        }
    }

    /// <summary>
    /// 检查账号配置
    /// </summary>
    private async Task<EYunCallbackProcessResult> CheckAccountConfigAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        // _logger.LogDebug("[{ProcessingId}] 🔍 开始账号配置检查 - WcId: {WcId}", processingId, callbackMessage.WcId);

        try
        {
            var isEnabled = await _accountConfigChecker.IsAccountEnabledAsync(callbackMessage.WcId!);
            
            if (!isEnabled)
            {
                // _logger.LogInformation("[{ProcessingId}] 账号已停用，消息将被丢弃 - WcId: {WcId}", 
                //     processingId, callbackMessage.WcId);
                
                return new EYunCallbackProcessResult
                {
                    Success = true,
                    ShouldContinue = false, // 账号停用，不继续处理但不算错误
                    ProcessingDetails = $"账号已停用: {callbackMessage.WcId}",
                    CompletedStage = EYunCallbackProcessStage.AccountConfigCheck
                };
            }

            // _logger.LogDebug("[{ProcessingId}] ✅ 账号配置检查通过 - WcId: {WcId}", processingId, callbackMessage.WcId);
            return new EYunCallbackProcessResult
            {
                Success = true,
                ShouldContinue = true,
                CompletedStage = EYunCallbackProcessStage.AccountConfigCheck
            };
        }
        catch (Exception ex)
        {
            // _logger.LogError(ex, "[{ProcessingId}] 账号配置检查异常 - WcId: {WcId}", processingId, callbackMessage.WcId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"账号配置检查异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.AccountConfigCheck
            };
        }
    }

    /// <summary>
    /// 加载全局机器人配置
    /// </summary>
    private async Task<EYunRiskControlConfigDto> LoadGlobalBotConfigAsync(string processingId)
    {
        _logger.LogDebug("[{ProcessingId}] 📖 加载全局机器人配置", processingId);

        try
        {
            var config = await _configManager.GetConfigAsync<EYunRiskControlConfigDto>(
                "EYunRiskControl",
                "EYunRiskControlConfig",
                () => new EYunRiskControlConfigDto
                {
                    EnableIncomingMessageSensitiveWordDetection = true,
                    EnableAutoAcceptFriends = false,

                    SensitiveWordAction = SensitiveWordActionType.Block,
                    MaxContentLength = 2000,
                    AutoAcceptFriendsPerDay = 120
                });

            // _logger.LogDebug("[{ProcessingId}] ✅ 全局机器人配置加载完成", processingId);
            return config ?? new EYunRiskControlConfigDto();
        }
        catch (Exception ex)
        {
            // _logger.LogError(ex, "[{ProcessingId}] 加载全局机器人配置异常，使用默认配置", processingId);
            return new EYunRiskControlConfigDto();
        }
    }

    /// <summary>
    /// 分类并处理消息
    /// </summary>
    private async Task<EYunCallbackProcessResult> ClassifyAndProcessMessageAsync(
        WxCallbackMessageDto callbackMessage,
        EYunRiskControlConfigDto globalConfig,
        string processingId,
        CancellationToken cancellationToken)
    {
        // _logger.LogDebug("[{ProcessingId}] 🔄 开始消息分类处理 - MessageType: {MessageType}",
        //     processingId, callbackMessage.MessageType);

        try
        {
            // 1. 使用消息类型分类器进行分类
            var classification = _messageTypeClassifier.ClassifyMessage(callbackMessage);

            // _logger.LogDebug("[{ProcessingId}] 消息分类完成 - Type: {Type}, Priority: {Priority}, Description: {Description}",
            //     processingId, classification.Type, classification.Priority, classification.Description);

            // 2. 根据分类结果进行处理
            return classification.Type switch
            {
                Models.MessageType.FriendRequest => await ProcessFriendRequestAsync(callbackMessage, globalConfig, processingId, cancellationToken),
                Models.MessageType.OfflineNotification => await ProcessOfflineNotificationAsync(callbackMessage, processingId, cancellationToken),
                Models.MessageType.PrivateText or Models.MessageType.PrivateImage or Models.MessageType.PrivateVoice or Models.MessageType.PrivateFile
                    => await ProcessPrivateMessageAsync(callbackMessage, globalConfig, classification, processingId, cancellationToken),
                Models.MessageType.GroupText or Models.MessageType.GroupImage or Models.MessageType.GroupVoice or Models.MessageType.GroupFile
                    => await ProcessGroupMessageAsync(callbackMessage, globalConfig, classification, processingId, cancellationToken),
                _ => await ProcessUnknownMessageAsync(callbackMessage, classification, processingId, cancellationToken)
            };
        }
        catch (Exception ex)
        {
            // _logger.LogError(ex, "[{ProcessingId}] 消息分类处理异常", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"消息分类处理异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.MessageTypeClassification
            };
        }
    }

    /// <summary>
    /// 处理好友请求
    /// </summary>
    private async Task<EYunCallbackProcessResult> ProcessFriendRequestAsync(
        WxCallbackMessageDto callbackMessage,
        EYunRiskControlConfigDto globalConfig,
        string processingId,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("[{ProcessingId}] 👥 处理好友请求 - MessageType: 30001", processingId);

        try
        {
            // 1. 检查是否启用自动通过好友
            if (!globalConfig.EnableAutoAcceptFriends)
            {
                _logger.LogDebug("[{ProcessingId}] 自动通过好友功能未启用", processingId);
                return new EYunCallbackProcessResult
                {
                    Success = true,
                    ShouldContinue = false,
                    ProcessingDetails = "自动通过好友功能未启用",
                    CompletedStage = EYunCallbackProcessStage.FriendRequestProcessing
                };
            }

            // 2. 解析好友请求信息
            var friendRequestInfo = _friendRequestParser.ParseFriendRequest(callbackMessage);
            if (friendRequestInfo == null)
            {
                // _logger.LogWarning("[{ProcessingId}] 解析好友请求信息失败", processingId);
                return new EYunCallbackProcessResult
                {
                    Success = false,
                    ShouldContinue = false,
                    ErrorMessage = "解析好友请求信息失败",
                    CompletedStage = EYunCallbackProcessStage.FriendRequestProcessing
                };
            }

            // _logger.LogInformation("[{ProcessingId}] 解析好友请求成功 - FromUser: {FromUser} ({FromNickname}), Scene: {Scene}",
            //     processingId, friendRequestInfo.FromUser, friendRequestInfo.FromNickname, friendRequestInfo.Scene);

            // 3. 检查关键词匹配（如果启用）
            if (globalConfig.EnableKeywordMatching && globalConfig.AutoAcceptKeywords?.Any() == true)
            {
                var containsKeyword = globalConfig.AutoAcceptKeywords.Any(keyword =>
                    friendRequestInfo.RequestMessage.Contains(keyword, StringComparison.OrdinalIgnoreCase));

                if (!containsKeyword)
                {
                    // _logger.LogDebug("[{ProcessingId}] 好友请求不包含通过关键词 - Message: {Message}",
                    //     processingId, friendRequestInfo.RequestMessage);
                    return new EYunCallbackProcessResult
                    {
                        Success = true,
                        ShouldContinue = false,
                        ProcessingDetails = "好友请求不包含通过关键词",
                        CompletedStage = EYunCallbackProcessStage.FriendRequestProcessing
                    };
                }
            }

            // 4. 检查今日通过数量限制
            var canAutoAccept = await _autoAcceptFriendService.CanAutoAcceptFriendAsync(
                callbackMessage.WxManagerId!, friendRequestInfo.FromUser);

            if (!canAutoAccept)
            {
                // _logger.LogDebug("[{ProcessingId}] 今日自动通过好友数量已达限制", processingId);
                return new EYunCallbackProcessResult
                {
                    Success = true,
                    ShouldContinue = false,
                    ProcessingDetails = "今日自动通过好友数量已达限制",
                    CompletedStage = EYunCallbackProcessStage.FriendRequestProcessing
                };
            }

            // 5. 调用EYun API通过好友请求
            var acceptRequest = new EYunAcceptUserRequest
            {
                WId = friendRequestInfo.WId,
                V1 = friendRequestInfo.V1,
                V2 = friendRequestInfo.V2,
                Type = friendRequestInfo.Scene
            };

            var acceptResult = await _eYunFriendRequestWrapper.AcceptUserAsync(acceptRequest);

            if (acceptResult?.IsSuccess == true)
            {
                _logger.LogInformation("[{ProcessingId}] ✅ 自动通过好友成功 - FromUser: {FromUser} ({FromNickname})",
                    processingId, friendRequestInfo.FromUser, friendRequestInfo.FromNickname);

                // 7. 发送欢迎语（如果配置了）
                if (!string.IsNullOrEmpty(globalConfig.FriendWelcomeMessage))
                {
                    await SendFriendWelcomeMessageAsync(friendRequestInfo, globalConfig.FriendWelcomeMessage, processingId);
                }

                return new EYunCallbackProcessResult
                {
                    Success = true,
                    ShouldContinue = false,
                    ProcessingDetails = "自动通过好友成功",
                    CompletedStage = EYunCallbackProcessStage.FriendRequestProcessing,
                    ExtendedData = {
                        ["AutoAccepted"] = true,
                        ["FromUser"] = friendRequestInfo.FromUser,
                        ["FromNickname"] = friendRequestInfo.FromNickname
                    }
                };
            }
            else
            {
                // _logger.LogWarning("[{ProcessingId}] 自动通过好友失败 - FromUser: {FromUser}, Error: {Error}",
                //     processingId, friendRequestInfo.FromUser, acceptResult?.Message);

                return new EYunCallbackProcessResult
                {
                    Success = false,
                    ShouldContinue = false,
                    ErrorMessage = $"自动通过好友失败: {acceptResult?.Message}",
                    CompletedStage = EYunCallbackProcessStage.FriendRequestProcessing
                };
            }
        }
        catch (Exception ex)
        {
            // _logger.LogError(ex, "[{ProcessingId}] 处理好友请求异常", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"处理好友请求异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.FriendRequestProcessing
            };
        }
    }

    /// <summary>
    /// 处理私聊消息
    /// </summary>
    private async Task<EYunCallbackProcessResult> ProcessPrivateMessageAsync(
        WxCallbackMessageDto callbackMessage,
        EYunRiskControlConfigDto globalConfig,
        MessageClassificationResult classification,
        string processingId,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("[{ProcessingId}] 📱 处理私聊消息 - MessageType: {MessageType}, Type: {Type}",
            processingId, callbackMessage.MessageType, classification.Type);

        try
        {
            // 1. 敏感词检测
            if (globalConfig.EnableIncomingMessageSensitiveWordDetection)
            {
                var sensitiveResult = await _sensitiveWordService.ProcessIncomingMessageAsync(
                    callbackMessage.Data?.Content ?? "",
                    $"private_{callbackMessage.MessageType}_{processingId}");

                if (sensitiveResult.ShouldBlock)
                {
                    // _logger.LogWarning("[{ProcessingId}] 私聊消息包含敏感词已拦截 - Words: {Words}",
                    //     processingId, string.Join(", ", sensitiveResult.DetectedWords));
                    return new EYunCallbackProcessResult
                    {
                        Success = true,
                        ShouldContinue = false,
                        ProcessingDetails = $"消息包含敏感词已拦截: {string.Join(", ", sensitiveResult.DetectedWords)}",
                        CompletedStage = EYunCallbackProcessStage.MessageProcessing
                    };
                }
            }

            // 2. 图文混排解析
            var parseResult = await _mixedContentParser.ParseAsync(callbackMessage);
            // _logger.LogDebug("[{ProcessingId}] 图文混排解析完成 - Items: {ItemCount}, HasMedia: {HasMedia}",
            //    processingId, parseResult.Items.Count, parseResult.HasMedia);

            // 3. 媒体文件处理
            MediaProcessResult? mediaResult = null;
            if (parseResult.HasMedia && _mediaMessageProcessor.ShouldProcessMedia(callbackMessage.MessageType!))
            {
                // 🔧 重构：使用60009处理文件发送完成消息
                mediaResult = callbackMessage.MessageType switch
                {
                    "60002" => await _mediaMessageProcessor.ProcessImageMessageAsync(callbackMessage, processingId),
                    "60004" => await _mediaMessageProcessor.ProcessVoiceMessageAsync(callbackMessage, processingId),
                    "60009" => await _mediaMessageProcessor.ProcessFileMessageAsync(callbackMessage, processingId), // 文件发送完成
                    _ => null
                };

                if (mediaResult != null && !mediaResult.IsSuccess)
                {
                    _logger.LogWarning("[{ProcessingId}] 媒体文件处理失败 - Error: {Error}",
                        processingId, mediaResult.ErrorMessage);
                }
                else if (mediaResult != null)
                {
                    _logger.LogDebug("[{ProcessingId}] 媒体文件处理成功 - FileName: {FileName}, Size: {Size}",
                        processingId, mediaResult.FileName, mediaResult.FileSize);
                }
            }

            // 4. AI配置验证（包含实体存在性检查）
            var configValidation = await _messageConfigValidator.ValidateForAiProcessingAsync(callbackMessage, processingId);
            _logger.LogDebug("[{ProcessingId}] AI配置验证完成 - ShouldProcessWithAi: {ShouldProcess}, ShouldDiscard: {ShouldDiscard}, EntityType: {EntityType}",
                processingId, configValidation.ShouldProcessWithAi, configValidation.ShouldDiscard, configValidation.EntityType);

            // 检查是否应该丢弃消息
            if (configValidation.ShouldDiscard)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 消息被丢弃 - 原因: {DiscardReason}, EntityType: {EntityType}",
                    processingId, configValidation.DiscardReason, configValidation.EntityType);

                // 记录消息丢弃监控
                try
                {
                    if (_messageProcessingMonitor != null)
                    {
                        await _messageProcessingMonitor.RecordMessageDiscardAsync(
                            callbackMessage.Data?.NewMsgId.ToString() ?? processingId,
                            callbackMessage.MessageType,
                            callbackMessage.WcId ?? "",
                            "ConfigValidationFailed",
                            configValidation.ValidationDetails ?? ""
                        );
                    }
                }
                catch (Exception monitorEx)
                {
                    _logger.LogWarning(monitorEx, "[{ProcessingId}] 记录消息丢弃监控失败", processingId);
                }

                return new EYunCallbackProcessResult
                {
                    Success = true,
                    ShouldContinue = false,
                    ProcessingDetails = $"消息已丢弃 - 原因: {configValidation.DiscardReason}",
                    CompletedStage = EYunCallbackProcessStage.MessageProcessing,
                    ExtendedData =
                    {
                        ["MessageDiscarded"] = true,
                        ["DiscardReason"] = configValidation.DiscardReason.ToString(),
                        ["EntityType"] = configValidation.EntityType ?? "",
                        ["ValidationDetails"] = configValidation.ValidationDetails ?? ""
                    }
                };
            }

            if (configValidation.ShouldProcessWithAi)
            {
                // 路由到AI处理队列
                return await RouteToAiProcessingAsync(callbackMessage, processingId, cancellationToken);
            }

            return new EYunCallbackProcessResult
            {
                Success = true,
                ShouldContinue = false,
                ProcessingDetails = $"私聊消息处理完成 - 解析项目: {parseResult.Items.Count}, 媒体处理: {(mediaResult?.IsSuccess == true ? "成功" : "跳过")}, AI配置: {(configValidation.ShouldProcessWithAi ? "启用" : "禁用")}",
                CompletedStage = EYunCallbackProcessStage.MessageProcessing,
                ExtendedData =
                {
                    ["ParsedItems"] = parseResult.Items.Count,
                    ["HasMedia"] = parseResult.HasMedia,
                    ["MediaProcessed"] = mediaResult?.IsSuccess == true,
                    ["ShouldProcessWithAi"] = configValidation.ShouldProcessWithAi,
                    ["EntityType"] = configValidation.EntityType ?? "",
                    ["EntityName"] = configValidation.EntityName ?? "",
                    ["ExtractedUrls"] = parseResult.ExtractedUrls,
                    ["AtMentions"] = parseResult.AtMentions
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理私聊消息异常", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"处理私聊消息异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.MessageProcessing
            };
        }
    }

    /// <summary>
    /// 处理群聊消息
    /// </summary>
    private async Task<EYunCallbackProcessResult> ProcessGroupMessageAsync(
        WxCallbackMessageDto callbackMessage,
        EYunRiskControlConfigDto globalConfig,
        MessageClassificationResult classification,
        string processingId,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("[{ProcessingId}] 👥 处理群聊消息 - MessageType: {MessageType}, Type: {Type}",
            processingId, callbackMessage.MessageType, classification.Type);

        try
        {
            // 1. 敏感词检测
            if (globalConfig.EnableIncomingMessageSensitiveWordDetection)
            {
                var sensitiveResult = await _sensitiveWordService.ProcessIncomingMessageAsync(
                    callbackMessage.Data?.Content ?? "",
                    $"group_{callbackMessage.MessageType}_{processingId}");

                if (sensitiveResult.ShouldBlock)
                {
                    _logger.LogWarning("[{ProcessingId}] 群聊消息包含敏感词已拦截 - Words: {Words}",
                        processingId, string.Join(", ", sensitiveResult.DetectedWords));
                    return new EYunCallbackProcessResult
                    {
                        Success = true,
                        ShouldContinue = false,
                        ProcessingDetails = $"消息包含敏感词已拦截: {string.Join(", ", sensitiveResult.DetectedWords)}",
                        CompletedStage = EYunCallbackProcessStage.MessageProcessing
                    };
                }
            }

            // 2. @用户检测
            var atDetectionResult = await _atUserDetectionService.DetectMentionInGroupMessageAsync(callbackMessage);
            _logger.LogDebug("[{ProcessingId}] @用户检测完成 - IsGroupMessage: {IsGroupMessage}, IsMentioned: {IsMentioned}, MentionedCount: {MentionedCount}",
                processingId, atDetectionResult.IsGroupMessage, atDetectionResult.IsMentioned, atDetectionResult.MentionedUsers.Count);

            // 如果有错误，记录但不阻止处理
            if (atDetectionResult.HasError)
            {
                _logger.LogWarning("[{ProcessingId}] @用户检测出现错误: {ErrorMessage}", processingId, atDetectionResult.ErrorMessage);
            }

            // 3. 图文混排解析
            var parseResult = await _mixedContentParser.ParseAsync(callbackMessage);
            _logger.LogDebug("[{ProcessingId}] 图文混排解析完成 - Items: {ItemCount}, HasMedia: {HasMedia}",
                processingId, parseResult.Items.Count, parseResult.HasMedia);

            // 4. 媒体文件处理
            MediaProcessResult? mediaResult = null;
            if (parseResult.HasMedia && _mediaMessageProcessor.ShouldProcessMedia(callbackMessage.MessageType!))
            {
                // 🔧 重构：使用80009替代80008处理群聊文件发送完成消息
                mediaResult = callbackMessage.MessageType switch
                {
                    "80002" => await _mediaMessageProcessor.ProcessImageMessageAsync(callbackMessage, processingId),
                    "80004" => await _mediaMessageProcessor.ProcessVoiceMessageAsync(callbackMessage, processingId),
                    "80009" => await _mediaMessageProcessor.ProcessFileMessageAsync(callbackMessage, processingId), // 群聊文件发送完成
                    _ => null
                };

                if (mediaResult != null && !mediaResult.IsSuccess)
                {
                    _logger.LogWarning("[{ProcessingId}] 媒体文件处理失败 - Error: {Error}",
                        processingId, mediaResult.ErrorMessage);
                }
                else if (mediaResult != null)
                {
                    _logger.LogDebug("[{ProcessingId}] 媒体文件处理成功 - FileName: {FileName}, Size: {Size}",
                        processingId, mediaResult.FileName, mediaResult.FileSize);
                }
            }

            // 5. AI配置验证（包含实体存在性检查）
            var configValidation = await _messageConfigValidator.ValidateForAiProcessingAsync(callbackMessage, processingId);
            _logger.LogDebug("[{ProcessingId}] AI配置验证完成 - ShouldProcessWithAi: {ShouldProcess}, ShouldDiscard: {ShouldDiscard}, EntityType: {EntityType}",
                processingId, configValidation.ShouldProcessWithAi, configValidation.ShouldDiscard, configValidation.EntityType);

            // 检查是否应该丢弃消息
            if (configValidation.ShouldDiscard)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 消息被丢弃 - 原因: {DiscardReason}, EntityType: {EntityType}",
                    processingId, configValidation.DiscardReason, configValidation.EntityType);

                return new EYunCallbackProcessResult
                {
                    Success = true,
                    ShouldContinue = false,
                    ProcessingDetails = $"消息已丢弃 - 原因: {configValidation.DiscardReason}",
                    CompletedStage = EYunCallbackProcessStage.MessageProcessing,
                    ExtendedData =
                    {
                        ["MessageDiscarded"] = true,
                        ["DiscardReason"] = configValidation.DiscardReason.ToString(),
                        ["EntityType"] = configValidation.EntityType ?? "",
                        ["ValidationDetails"] = configValidation.ValidationDetails ?? ""
                    }
                };
            }

            if (configValidation.ShouldProcessWithAi)
            {
                // 路由到AI处理队列
                return await RouteToAiProcessingAsync(callbackMessage, processingId, cancellationToken);
            }

            return new EYunCallbackProcessResult
            {
                Success = true,
                ShouldContinue = false,
                ProcessingDetails = $"群聊消息处理完成 - 解析项目: {parseResult.Items.Count}, 媒体处理: {(mediaResult?.IsSuccess == true ? "成功" : "跳过")}, AI配置: {(configValidation.ShouldProcessWithAi ? "启用" : "禁用")}",
                CompletedStage = EYunCallbackProcessStage.MessageProcessing,
                ExtendedData =
                {
                    ["ParsedItems"] = parseResult.Items.Count,
                    ["HasMedia"] = parseResult.HasMedia,
                    ["MediaProcessed"] = mediaResult?.IsSuccess == true,
                    ["ShouldProcessWithAi"] = configValidation.ShouldProcessWithAi,
                    ["EntityType"] = configValidation.EntityType ?? "",
                    ["EntityName"] = configValidation.EntityName ?? "",
                    ["ExtractedUrls"] = parseResult.ExtractedUrls,
                    ["AtMentions"] = parseResult.AtMentions,
                    ["IsGroupMessage"] = atDetectionResult.IsGroupMessage,
                    ["IsMentioned"] = atDetectionResult.IsMentioned,
                    ["MentionedUsers"] = atDetectionResult.MentionedUsers,
                    ["CleanContent"] = atDetectionResult.CleanContent ?? callbackMessage.Data?.Content ?? ""
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理群聊消息异常", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"处理群聊消息异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.MessageProcessing
            };
        }
    }

    /// <summary>
    /// 处理未知消息类型
    /// </summary>
    private async Task<EYunCallbackProcessResult> ProcessUnknownMessageAsync(
        WxCallbackMessageDto callbackMessage,
        MessageClassificationResult classification,
        string processingId,
        CancellationToken cancellationToken)
    {
        _logger.LogWarning("[{ProcessingId}] ❓ 处理未知消息类型 - MessageType: {MessageType}, Description: {Description}",
            processingId, callbackMessage.MessageType, classification.Description);

        return new EYunCallbackProcessResult
        {
            Success = true,
            ShouldContinue = false,
            ProcessingDetails = $"未知消息类型: {callbackMessage.MessageType}",
            CompletedStage = EYunCallbackProcessStage.MessageProcessing
        };
    }



    /// <summary>
    /// 路由到AI处理
    /// </summary>
    private async Task<EYunCallbackProcessResult> RouteToAiProcessingAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 路由消息到AI处理队列", processingId);

            // 路由到AI处理队列
            var routeResult = await _messageRouter.RouteWxCallbackMessageAsync(callbackMessage, processingId, cancellationToken);

            // 🔧 精简日志 - 注释掉消息路由到AI处理队列日志，减少日志噪音
            // _logger.LogInformation("[{ProcessingId}] 消息已路由到AI处理队列 - QueueId: {QueueId}",
            //     processingId, routeResult);

            return new EYunCallbackProcessResult
            {
                Success = true,
                ShouldContinue = false,
                ProcessingDetails = "消息已路由到AI处理队列",
                CompletedStage = EYunCallbackProcessStage.QueueRouting,
                ExtendedData = { ["QueueId"] = routeResult }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 路由到AI处理异常", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"路由到AI处理异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.QueueRouting
            };
        }
    }

    /// <summary>
    /// 发送好友欢迎语
    /// </summary>
    private async Task SendFriendWelcomeMessageAsync(HappyWechat.Infrastructure.MessageProcessing.Models.FriendRequestInfo friendInfo, string welcomeMessage, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 准备发送好友欢迎语 - ToUser: {ToUser}", processingId, friendInfo.FromUser);

            // 替换欢迎语中的变量
            var processedMessage = welcomeMessage
                .Replace("{friendName}", friendInfo.FromNickname)
                .Replace("{currentTime}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            // 这里可以调用消息发送服务发送欢迎语
            // 暂时只记录日志
            _logger.LogInformation("[{ProcessingId}] 好友欢迎语已准备 - ToUser: {ToUser}, Message: {Message}",
                processingId, friendInfo.FromUser, processedMessage);

            // TODO: 实际发送欢迎语的逻辑
            // await _messageRouter.RouteTextMessageAsync(new TextMessageRequest
            // {
            //     WxManagerId = friendInfo.WId,
            //     ToUser = friendInfo.FromUser,
            //     Content = processedMessage
            // });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 发送好友欢迎语失败 - ToUser: {ToUser}", processingId, friendInfo.FromUser);
        }
    }

    /// <summary>
    /// 处理离线通知
    /// </summary>
    private async Task<EYunCallbackProcessResult> ProcessOfflineNotificationAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("[{ProcessingId}] 📴 处理离线通知", processingId);

        try
        {
            // 使用离线通知处理器处理
            var notificationResult = await _offlineNotificationProcessor.ProcessOfflineNotificationAsync(callbackMessage, processingId);

            // _logger.LogInformation("[{ProcessingId}] 离线通知处理完成 - WcId: {WcId}, 成功: {Success}",
            //     processingId, callbackMessage.WcId, notificationResult.Success); // 已简化，避免冗余日志

            return new EYunCallbackProcessResult
            {
                Success = notificationResult.Success,
                ShouldContinue = false, // 离线通知处理完成，不需要继续
                ProcessingDetails = notificationResult.Message ?? notificationResult.ErrorMessage ?? "离线通知处理完成",
                CompletedStage = EYunCallbackProcessStage.MessageProcessing,
                ExtendedData =
                {
                    ["NotificationSuccess"] = notificationResult.Success,
                    ["Message"] = notificationResult.Message ?? "",
                    ["ErrorMessage"] = notificationResult.ErrorMessage ?? ""
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理离线通知异常", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"处理离线通知异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.MessageProcessing
            };
        }
    }



    /// <summary>
    /// 路由到消息队列
    /// </summary>
    private async Task<EYunCallbackProcessResult> RouteToMessageQueueAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("[{ProcessingId}] 🚀 路由消息到队列", processingId);

        try
        {
            var messageId = await _messageRouter.RouteWxCallbackMessageAsync(callbackMessage, processingId, cancellationToken);

            _logger.LogInformation("[{ProcessingId}] ✅ 消息路由成功 - MessageId: {MessageId}",
                processingId, messageId);

            return new EYunCallbackProcessResult
            {
                Success = true,
                ShouldContinue = true,
                ProcessingDetails = $"消息路由成功: {messageId}",
                CompletedStage = EYunCallbackProcessStage.QueueRouting,
                ExtendedData = { ["QueueMessageId"] = messageId }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 消息路由异常", processingId);
            return new EYunCallbackProcessResult
            {
                Success = false,
                ShouldContinue = false,
                ErrorMessage = $"消息路由异常: {ex.Message}",
                CompletedStage = EYunCallbackProcessStage.QueueRouting
            };
        }
    }

    /// <summary>
    /// 判断是否应该重试处理
    /// </summary>
    private bool ShouldRetryProcessing(Exception ex)
    {
        // 定义可重试的异常类型
        var retryableExceptions = new[]
        {
            typeof(TimeoutException),
            typeof(HttpRequestException),
            typeof(TaskCanceledException),
            typeof(OperationCanceledException)
        };

        // 检查异常类型
        var exceptionType = ex.GetType();
        if (retryableExceptions.Contains(exceptionType))
        {
            return true;
        }

        // 检查内部异常
        if (ex.InnerException != null)
        {
            return ShouldRetryProcessing(ex.InnerException);
        }

        // 检查异常消息中的关键词
        var retryableKeywords = new[] { "timeout", "network", "connection", "temporary" };
        var message = ex.Message.ToLower();
        if (retryableKeywords.Any(keyword => message.Contains(keyword)))
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 配置变化时的回调
    /// </summary>
    public async Task OnConfigurationChangedAsync(ConfigurationChangeEvent changeEvent)
    {
        try
        {
            _logger.LogInformation("🔄 检测到配置变化 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}, ChangeId: {ChangeId}",
                changeEvent.ConfigType, changeEvent.ConfigKey, changeEvent.ChangeId);

            var shouldClearCache = false;
            var cacheDescription = "";

            // 处理不同类型的配置变化
            switch (changeEvent.ConfigType)
            {
                case "SystemConfig":
                    if (changeEvent.ConfigKey == "EYunRiskControl")
                    {
                        shouldClearCache = true;
                        cacheDescription = "EYun风控配置";
                    }
                    break;

                case "AiConfig":
                case "ContactAiConfig":
                case "GroupAiConfig":
                    // AI配置变化，清除相关缓存
                    shouldClearCache = true;
                    cacheDescription = "AI配置";
                    break;

                case "GlobalConfig":
                    // 全局配置变化，清除全局配置缓存
                    shouldClearCache = true;
                    cacheDescription = "全局机器人配置";
                    break;
            }

            if (shouldClearCache)
            {
                lock (_configLock)
                {
                    _cachedConfig = null; // 清除缓存，下次使用时重新加载
                }

                _logger.LogInformation("✅ {CacheDescription}缓存已清除，将在下次使用时重新加载 - ChangeId: {ChangeId}",
                    cacheDescription, changeEvent.ChangeId);

                // 记录配置变更监控
                try
                {
                    if (_messageProcessingMonitor != null)
                    {
                        await _messageProcessingMonitor.RecordConfigurationChangeAsync(
                            changeEvent.ConfigType,
                            $"ConfigKey: {changeEvent.ConfigKey}, ChangeId: {changeEvent.ChangeId}"
                        );
                    }
                }
                catch (Exception monitorEx)
                {
                    _logger.LogWarning(monitorEx, "记录配置变更监控失败 - ChangeId: {ChangeId}", changeEvent.ChangeId);
                }

                // 可选：立即预热缓存
                if (changeEvent.ConfigType == "GlobalConfig")
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await GetCachedGlobalConfigAsync($"preload_{changeEvent.ChangeId}");
                            _logger.LogDebug("全局配置缓存预热完成 - ChangeId: {ChangeId}", changeEvent.ChangeId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "全局配置缓存预热失败 - ChangeId: {ChangeId}", changeEvent.ChangeId);
                        }
                    });
                }
            }
            else
            {
                _logger.LogDebug("配置变化不影响当前处理器缓存 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}",
                    changeEvent.ConfigType, changeEvent.ConfigKey);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理配置变化异常 - ConfigType: {ConfigType}, ConfigKey: {ConfigKey}, ChangeId: {ChangeId}",
                changeEvent.ConfigType, changeEvent.ConfigKey, changeEvent.ChangeId);
        }
    }

    /// <summary>
    /// 获取缓存的全局配置
    /// </summary>
    private async Task<EYunRiskControlConfigDto> GetCachedGlobalConfigAsync(string processingId)
    {
        lock (_configLock)
        {
            if (_cachedConfig != null)
            {
                return _cachedConfig;
            }
        }

        // 缓存为空，重新加载
        var config = await LoadGlobalBotConfigAsync(processingId);

        lock (_configLock)
        {
            _cachedConfig = config;
        }

        return config;
    }

    /// <summary>
    /// 验证实体存在性 - 检查联系人或群组是否在数据库中存在
    /// </summary>
    private async Task<EYunCallbackProcessResult> ValidateEntityExistenceAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🔍 开始实体存在性验证", processingId);

            if (!Guid.TryParse(callbackMessage.WxManagerId, out var wxManagerId))
            {
                _logger.LogError("[{ProcessingId}] WxManagerId格式无效 - WxManagerId: {WxManagerId}",
                    processingId, callbackMessage.WxManagerId);
                return EYunCallbackProcessResult.CreateFailure(
                    "WxManagerId格式无效",
                    EYunCallbackProcessStage.EntityValidation);
            }

            // 检查群组消息
            if (!string.IsNullOrEmpty(callbackMessage.Data?.FromGroup))
            {
                var fromGroup = callbackMessage.Data.FromGroup;

                // 使用缓存优化群组存在性查询
                var groupCacheKey = $"entity_exists_group_{wxManagerId}_{fromGroup}";
                var cachedGroupExists = await _cacheManager.GetAsync<bool?>(groupCacheKey);

                bool groupExists;
                if (cachedGroupExists.HasValue)
                {
                    groupExists = cachedGroupExists.Value;
                    _logger.LogDebug("[{ProcessingId}] 🎯 群组存在性缓存命中 - FromGroup: {FromGroup}", processingId, fromGroup);
                }
                else
                {
                    groupExists = await _dbContext.WxGroupEntities
                        .AnyAsync(g => g.ChatRoomId == fromGroup && g.WxManagerId == wxManagerId);

                    // 缓存结果30分钟
                    await _cacheManager.SetAsync(groupCacheKey, groupExists, TimeSpan.FromMinutes(30));
                    _logger.LogDebug("[{ProcessingId}] 🔍 群组存在性数据库查询完成并缓存 - FromGroup: {FromGroup}, Exists: {Exists}",
                        processingId, fromGroup, groupExists);
                }

                if (!groupExists)
                {
                    // 记录统一消息接收日志，包含群组不存在的提示
                    await LogUnifiedMessageReceivedAsync(processingId, callbackMessage, wxManagerId, false, "数据库没有当前群组");

                    // 注释掉原有的详细诊断日志，避免冗余
                    // _logger.LogWarning("[{ProcessingId}] ⚠️ 群组不存在，消息被丢弃 - FromGroup: {FromGroup}, WxManagerId: {WxManagerId}",
                    //     processingId, fromGroup, wxManagerId);
                    // _logger.LogInformation("[{ProcessingId}] 📊 群组不存在诊断信息:", processingId);
                    // _logger.LogInformation("[{ProcessingId}]   - 群组ID: {FromGroup}", processingId, fromGroup);
                    // _logger.LogInformation("[{ProcessingId}]   - 微信管理器ID: {WxManagerId}", processingId, wxManagerId);
                    // _logger.LogInformation("[{ProcessingId}]   - 消息类型: {MessageType}", processingId, callbackMessage.MessageType);
                    // _logger.LogInformation("[{ProcessingId}]   - 建议: 请检查群组是否已同步到数据库，或联系人列表是否需要更新", processingId);

                    return EYunCallbackProcessResult.CreateFailure(
                        $"群组 {fromGroup} 在数据库中不存在",
                        EYunCallbackProcessStage.EntityValidation,
                        new Dictionary<string, object>
                        {
                            ["FromGroup"] = fromGroup,
                            ["WxManagerId"] = wxManagerId,
                            ["DiscardReason"] = "GroupNotExists",
                            ["MessageType"] = callbackMessage.MessageType ?? "",
                            ["DiagnosticInfo"] = "群组未在WxGroups表中找到对应记录"
                        });
                }

                _logger.LogDebug("[{ProcessingId}] ✅ 群组存在性验证通过 - FromGroup: {FromGroup}",
                    processingId, fromGroup);
            }
            // 检查联系人消息
            else if (!string.IsNullOrEmpty(callbackMessage.Data?.FromUser))
            {
                var fromUser = callbackMessage.Data.FromUser;

                // 使用缓存优化联系人存在性查询
                var contactCacheKey = $"entity_exists_contact_{wxManagerId}_{fromUser}";
                var cachedContactExists = await _cacheManager.GetAsync<bool?>(contactCacheKey);

                bool contactExists;
                if (cachedContactExists.HasValue)
                {
                    contactExists = cachedContactExists.Value;
                    _logger.LogDebug("[{ProcessingId}] 🎯 联系人存在性缓存命中 - FromUser: {FromUser}", processingId, fromUser);
                }
                else
                {
                    contactExists = await _dbContext.WxContactEntities
                        .AnyAsync(c => c.WcId == fromUser && c.WxManagerId == wxManagerId);

                    // 缓存结果30分钟
                    await _cacheManager.SetAsync(contactCacheKey, contactExists, TimeSpan.FromMinutes(30));
                    _logger.LogDebug("[{ProcessingId}] 🔍 联系人存在性数据库查询完成并缓存 - FromUser: {FromUser}, Exists: {Exists}",
                        processingId, fromUser, contactExists);
                }

                if (!contactExists)
                {
                    // 记录统一消息接收日志，包含联系人不存在的提示
                    await LogUnifiedMessageReceivedAsync(processingId, callbackMessage, wxManagerId, false, "数据库没有当前联系人");

                    // 注释掉原有的详细诊断日志，避免冗余
                    _logger.LogWarning("[{ProcessingId}] ⚠️ 联系人不存在，消息被丢弃 - FromUser: {FromUser}, WxManagerId: {WxManagerId}",
                        processingId, fromUser, wxManagerId);
                    // _logger.LogInformation("[{ProcessingId}] 📊 联系人不存在诊断信息:", processingId);
                    // _logger.LogInformation("[{ProcessingId}]   - 联系人微信ID: {FromUser}", processingId, fromUser);
                    // _logger.LogInformation("[{ProcessingId}]   - 微信管理器ID: {WxManagerId}", processingId, wxManagerId);
                    // _logger.LogInformation("[{ProcessingId}]   - 消息类型: {MessageType}", processingId, callbackMessage.MessageType);
                    // _logger.LogInformation("[{ProcessingId}]   - 消息内容: {Content}", processingId, callbackMessage.Data?.Content?.Substring(0, Math.Min(50, callbackMessage.Data?.Content?.Length ?? 0)) + "...");
                    // _logger.LogInformation("[{ProcessingId}]   - 建议: 请检查联系人是否已同步到数据库，或通讯录是否需要更新", processingId);

                    return EYunCallbackProcessResult.CreateFailure(
                        $"联系人 {fromUser} 在数据库中不存在",
                        EYunCallbackProcessStage.EntityValidation,
                        new Dictionary<string, object>
                        {
                            ["FromUser"] = fromUser,
                            ["WxManagerId"] = wxManagerId,
                            ["DiscardReason"] = "ContactNotExists",
                            ["MessageType"] = callbackMessage.MessageType ?? "",
                            ["MessageContent"] = callbackMessage.Data?.Content?.Substring(0, Math.Min(100, callbackMessage.Data?.Content?.Length ?? 0)) ?? "",
                            ["DiagnosticInfo"] = "联系人未在WxContactEntities表中找到对应记录"
                        });
                }

                _logger.LogDebug("[{ProcessingId}] ✅ 联系人存在性验证通过 - FromUser: {FromUser}",
                    processingId, fromUser);
            }

            // 🔧 消息接收日志已在主流程中记录，此处不再重复记录
            // await LogUnifiedMessageReceivedAsync(processingId, callbackMessage, wxManagerId, true);

            // _logger.LogDebug("[{ProcessingId}] ✅ 实体存在性验证完成", processingId); // 已合并到统一日志中
            return EYunCallbackProcessResult.CreateSuccess(
                "实体存在性验证通过",
                EYunCallbackProcessStage.EntityValidation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 实体存在性验证异常", processingId);
            return EYunCallbackProcessResult.CreateFailure(
                $"实体存在性验证异常: {ex.Message}",
                EYunCallbackProcessStage.EntityValidation);
        }
    }

    /// <summary>
    /// 记录统一格式的消息接收日志，包含实体验证结果
    /// </summary>
    private async Task LogUnifiedMessageReceivedAsync(string processingId, WxCallbackMessageDto callbackMessage, Guid wxManagerId, bool entityExists, string? entityValidationMessage = null)
    {
        try
        {
            var messageType = callbackMessage?.MessageType ?? "未知";
            var wxId = callbackMessage?.WcId ?? "未知";
            var fromUser = callbackMessage?.Data?.FromUser ?? "未知";
            // 🎯 通过数据库查询获取发送者昵称
            var fromUserNickName = "未知";
            if (!string.IsNullOrEmpty(callbackMessage?.Data?.FromUser))
            {
                try
                {
                    if (!string.IsNullOrEmpty(callbackMessage.Data.FromGroup))
                    {
                        // 群消息：从WxGroupMembers表获取昵称
                        fromUserNickName = await _contactNicknameService.GetGroupMemberNicknameAsync(
                            wxManagerId, callbackMessage.Data.FromGroup, callbackMessage.Data.FromUser);
                    }
                    else
                    {
                        // 私聊消息：从WxContactEntities表获取昵称
                        fromUserNickName = await _contactNicknameService.GetContactNicknameAsync(
                            wxManagerId, callbackMessage.Data.FromUser);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取发送者昵称失败，使用默认值 - FromUser: {FromUser}", callbackMessage.Data.FromUser);
                    fromUserNickName = "未知";
                }
            }
            var content = callbackMessage?.Data?.Content ?? "";
            var fromGroup = callbackMessage?.Data?.FromGroup;

            // 截断过长的内容
            if (content.Length > 100)
            {
                content = content[..100] + "...";
            }

            // 🎯 构建标准格式的消息接收日志
            var logMessage = $"[{processingId}] 收到微信消息回调 - 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}，基础信息 - 消息类型: {messageType}, 微信账号: {wxId}";

            if (!string.IsNullOrEmpty(fromGroup))
            {
                // 群聊消息格式 - 包含群组、发送者、发送者昵称
                logMessage += $", 群组：{fromGroup}，发送者: {fromUser}，发送者昵称: {fromUserNickName}，消息内容: {content}";
            }
            else
            {
                // 私聊消息格式 - 包含发送者昵称
                logMessage += $", 发送者: {fromUser}，发送者昵称: {fromUserNickName}，消息内容: {content}";
            }

            // 添加实体验证结果
            if (!entityExists && !string.IsNullOrEmpty(entityValidationMessage))
            {
                logMessage += $" - {entityValidationMessage}";
            }

            _logger.LogInformation(logMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 记录统一消息接收日志失败", processingId);
        }
    }
}
