using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Pipeline;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Pipeline.Handlers;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.Integration;
using HappyWechat.Infrastructure.MessageProcessing;
using HappyWechat.Infrastructure.Monitoring;
using HappyWechat.Infrastructure.ConfigSync;
using HappyWechat.Infrastructure.MessageQueue.Redis;
// using HappyWechat.Infrastructure.MessageQueue.Pipeline; // Old pipeline removed

namespace HappyWechat.Infrastructure.MessageQueue.Simplified;

/// <summary>
/// 简化队列服务注册扩展 - 完全替代旧架构
/// 注册所有新架构的服务和消费者
/// </summary>
public static class SimplifiedServiceExtensions
{
    /// <summary>
    /// 注册简化的消息队列服务 - 完整的新架构实现
    /// </summary>
    public static IServiceCollection AddSimplifiedMessageQueue(this IServiceCollection services)
    {
        // 核心队列服务
        services.AddSingleton<ISimplifiedQueueService, SimplifiedQueueService>();
        
        // 消息管道编排器 - 替代旧的MessagePipelineOrchestrator
        services.AddScoped<ISimplifiedMessagePipelineOrchestrator, SimplifiedMessagePipelineOrchestrator>();
        
        // 管道处理器 - 替代旧的所有Handler
        services.AddScoped<SimplifiedAccountConfigHandler>();
        services.AddScoped<SimplifiedSensitiveWordHandler>();
        services.AddScoped<SimplifiedFriendRequestHandler>();
        services.AddScoped<SimplifiedGroupInviteHandler>();
        services.AddScoped<SimplifiedMessageTypeHandler>();
        // SimplifiedAiReplyHandler已删除，使用新的AI处理流程
        services.AddScoped<SimplifiedFileProcessHandler>();
        
        // 消费者后台服务 - 替代旧的所有Consumer
        services.AddHostedService<Consumers.SimplifiedAiMessageConsumer>();
        services.AddHostedService<Consumers.SimplifiedWxCallbackConsumer>();
        services.AddHostedService<Consumers.SimplifiedContactSyncConsumer>();
        services.AddHostedService<Consumers.SimplifiedGroupSyncConsumer>();
        services.AddHostedService<Consumers.SimplifiedFileSendConsumer>();

        // 消息发送队列消费者 - 按消息类型隔离
        services.AddHostedService<Consumers.SimplifiedTextSendConsumer>();
        services.AddHostedService<Consumers.SimplifiedImageSendConsumer>();
        
        // 延时消息处理器 - 处理延时队列
        services.AddHostedService<SimplifiedDelayedMessageProcessor>();

        // 业务服务实现 - 替代旧架构的复杂服务
        services.AddScoped<IAiService, SimplifiedAiService>();
        services.AddScoped<IContactSyncService, SimplifiedContactSyncService>();
        services.AddScoped<IGroupSyncService, SimplifiedGroupSyncService>();
        services.AddScoped<IFileSendService, SimplifiedFileSendService>();

        // 新架构服务
        services.AddScoped<IUnifiedArchitectureIntegrationService, UnifiedArchitectureIntegrationService>();
        services.AddScoped<IEYunCallbackProcessor, EYunCallbackProcessor>();
        services.AddScoped<IMessageTypeFilter, MessageTypeFilter>();
        services.AddScoped<IUnifiedConfigService, UnifiedConfigService>();
        
        // 🚀 补充缺失的消息处理服务注册
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IEnhancedGroupAiProcessor, 
            HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupAiProcessor>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IPrivateMessageProcessor, 
            HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IEnhancedGroupMessageProcessor, 
            HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IContactAiConfigChecker, 
            HappyWechat.Infrastructure.MessageProcessing.Services.ContactAiConfigChecker>();
        
        // 监控服务
        services.AddSingleton<BusinessMetricsCollector>();
        services.AddSingleton<AiPerformanceMonitor>();
        services.AddSingleton<MessageDeliveryMonitor>();
        services.AddSingleton<FileProcessingMonitor>();
        services.AddSingleton<AlertManager>();
        services.AddScoped<IPerformanceMonitor, PerformanceMonitor>();
        
        // 配置同步服务
        services.AddScoped<IConfigSyncMonitor, ConfigSyncMonitor>();
        
        // Redis队列服务
        services.AddScoped<RedisQueueService>();
        
        // 旧的消息管道服务已被SimplifiedMessagePipelineOrchestrator替代
        services.AddScoped<IEnhancedRedisDelayedMessageService, EnhancedRedisDelayedMessageService>();
        services.AddScoped<IWxManagerIdQueueIsolationService, WxManagerIdQueueIsolationService>();

        return services;
    }

    /// <summary>
    /// 移除旧架构的服务注册（清理冗余）
    /// </summary>
    public static IServiceCollection RemoveOldMessageQueueServices(this IServiceCollection services)
    {
        // 移除旧的消息队列相关服务
        // 注意：这个方法在实际使用时需要小心，确保不会影响其他功能
        
        // 可以移除的旧服务类型（示例）：
        // - IMessagePipelineOrchestrator
        // - IRedisQueueService 
        // - RedisAiMessageConsumer
        // - RedisWxCallbackMessageConsumer
        // - 等等...
        
        return services;
    }
}