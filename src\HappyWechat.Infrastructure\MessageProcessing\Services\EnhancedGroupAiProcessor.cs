using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.AI;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.Constants;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Identity.Repositories;
using Microsoft.EntityFrameworkCore;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 增强的群消息AI处理器 - 支持完整的群聊@逻辑和混合消息类型
/// </summary>
public interface IEnhancedGroupAiProcessor
{
    /// <summary>
    /// 处理群聊AI回复（含@信息和混合消息支持）
    /// </summary>
    Task<GroupAiProcessResult> ProcessGroupAiReplyAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 群聊AI处理结果
/// </summary>
public class GroupAiProcessResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ProcessingDetails { get; set; }
    public bool ShouldContinue { get; set; } = true;
    public GroupMessageProcessResult? GroupConfig { get; set; }
    public bool WasAtReply { get; set; } = false;

    public static GroupAiProcessResult CreateSuccess(string details, GroupMessageProcessResult? groupConfig = null, bool wasAtReply = false)
    {
        return new GroupAiProcessResult
        {
            Success = true,
            ShouldContinue = true,
            ProcessingDetails = details,
            GroupConfig = groupConfig,
            WasAtReply = wasAtReply
        };
    }

    public static GroupAiProcessResult CreateFailure(string errorMessage, GroupMessageProcessResult? groupConfig = null)
    {
        return new GroupAiProcessResult
        {
            Success = false,
            ShouldContinue = false,
            ErrorMessage = errorMessage,
            GroupConfig = groupConfig
        };
    }

    public static GroupAiProcessResult CreateSkipped(string reason, GroupMessageProcessResult? groupConfig = null)
    {
        return new GroupAiProcessResult
        {
            Success = true,
            ShouldContinue = false,
            ProcessingDetails = reason,
            GroupConfig = groupConfig
        };
    }
}

/// <summary>
/// 增强的群消息AI处理器实现
/// </summary>
public class EnhancedGroupAiProcessor : IEnhancedGroupAiProcessor
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IEnhancedGroupMessageProcessor _groupMessageProcessor;
    private readonly IAiService _aiService;
    private readonly IWxMessageService _wxMessageService;
    private readonly ILogger<EnhancedGroupAiProcessor> _logger;

    public EnhancedGroupAiProcessor(
        ApplicationDbContext dbContext,
        IEnhancedGroupMessageProcessor groupMessageProcessor,
        IAiService aiService,
        IWxMessageService wxMessageService,
        ILogger<EnhancedGroupAiProcessor> logger)
    {
        _dbContext = dbContext;
        _groupMessageProcessor = groupMessageProcessor;
        _aiService = aiService;
        _wxMessageService = wxMessageService;
        _logger = logger;
    }

    public async Task<GroupAiProcessResult> ProcessGroupAiReplyAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var fromGroup = callbackMessage.Data?.FromGroup;
            var senderWcId = callbackMessage.Data?.FromUser;

            _logger.LogDebug("[{ProcessingId}] 🏠 开始处理群聊AI回复（含@信息） - 发送者: {SenderWcId}, 群组: {GroupId}, 消息类型: {MessageType}",
                processingId, senderWcId, fromGroup, callbackMessage.MessageType);

            // 1. 基础验证
            if (string.IsNullOrEmpty(fromGroup) || string.IsNullOrEmpty(senderWcId))
            {
                return GroupAiProcessResult.CreateFailure("群组或发送者信息缺失");
            }

            // 2. 群消息AI回复触发条件检查
            var shouldTriggerResult = await _groupMessageProcessor.ShouldTriggerAiReplyAsync(callbackMessage);
            
            if (!shouldTriggerResult.ShouldReply)
            {
                _logger.LogDebug("[{ProcessingId}] 🚫 群聊AI回复跳过 - 原因: {Reason}, 群组: {GroupName}",
                    processingId, shouldTriggerResult.Reason, shouldTriggerResult.GroupName);
                return GroupAiProcessResult.CreateSkipped(shouldTriggerResult.Reason, shouldTriggerResult);
            }

            // 3. 获取群成员昵称（用于@显示）
            var senderInfo = await GetGroupMemberInfoAsync(callbackMessage, processingId, cancellationToken);

            // 4. 处理AI回复
            var aiResult = await ProcessGroupAiReplyAsync(callbackMessage, shouldTriggerResult, senderInfo, processingId, cancellationToken);
            
            if (!aiResult.Success)
            {
                return GroupAiProcessResult.CreateFailure(aiResult.ErrorMessage!, shouldTriggerResult);
            }

            // 5. 发送群聊回复（含@逻辑）
            var sendResult = await SendGroupReplyWithAtAsync(callbackMessage, aiResult.AiResponse!, senderInfo, shouldTriggerResult.IsMentioned, processingId, cancellationToken);
            
            if (sendResult.Success)
            {
                _logger.LogInformation("[{ProcessingId}] ✅ 群聊AI回复处理完成 - 群组: {GroupName}, 发送者: {SenderNickName}, 是否@回复: {ShouldAt}",
                    processingId, shouldTriggerResult.GroupName, senderInfo.NickName, shouldTriggerResult.IsMentioned);
                return GroupAiProcessResult.CreateSuccess("群聊AI回复处理完成", shouldTriggerResult, shouldTriggerResult.IsMentioned);
            }
            else
            {
                return GroupAiProcessResult.CreateFailure($"群聊回复发送失败: {sendResult.ErrorMessage}", shouldTriggerResult);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 处理群聊AI回复失败 - 发送者: {SenderWcId}", processingId, callbackMessage.Data?.FromUser);
            return GroupAiProcessResult.CreateFailure($"处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取群成员信息
    /// </summary>
    private async Task<(string NickName, string WcId)> GetGroupMemberInfoAsync(
        WxCallbackMessageDto callbackMessage,
        string processingId,
        CancellationToken cancellationToken)
    {
        try
        {
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var fromGroup = callbackMessage.Data!.FromGroup;
            var senderWcId = callbackMessage.Data.FromUser!;

            // 查询群成员昵称
            var memberInfo = await _dbContext.WxGroupMemberEntities
                .Include(m => m.Group)
                .Where(m => m.Group!.WxManagerId == wxManagerId && m.ChatRoomId == fromGroup && m.UserName == senderWcId)
                .Select(m => new { m.NickName, WcId = m.UserName })
                .FirstOrDefaultAsync(cancellationToken);

            if (memberInfo != null)
            {
                return (memberInfo.NickName ?? senderWcId, memberInfo.WcId);
            }

            // 如果群成员表中找不到，尝试从联系人表查询
            var contactInfo = await _dbContext.WxContactEntities
                .Where(c => c.WxManagerId == wxManagerId && c.WcId == senderWcId)
                .Select(c => new { c.NickName, c.WcId })
                .FirstOrDefaultAsync(cancellationToken);

            return contactInfo != null 
                ? (contactInfo.NickName ?? senderWcId, contactInfo.WcId)
                : (senderWcId, senderWcId); // 默认使用WcId作为昵称
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "[{ProcessingId}] ⚠️ 获取群成员信息失败，使用默认值", processingId);
            return (callbackMessage.Data?.FromUser ?? "未知用户", callbackMessage.Data?.FromUser ?? "");
        }
    }

    /// <summary>
    /// 处理群聊AI回复
    /// </summary>
    private async Task<(bool Success, string? ErrorMessage, AiMessageDto? AiResponse)> ProcessGroupAiReplyAsync(
        WxCallbackMessageDto callbackMessage,
        GroupMessageProcessResult groupConfig,
        (string NickName, string WcId) senderInfo,
        string processingId,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🤖 开始群聊AI处理 - 群组: {GroupName}, 发送者: {SenderNickName}",
                processingId, groupConfig.GroupName, senderInfo.NickName);

            // 获取WxManager信息用于AI传参
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var wxManager = await _dbContext.WxManagerEntities
                .Where(w => w.Id == wxManagerId)
                .Select(w => new { w.WId, w.WcId })
                .FirstOrDefaultAsync(cancellationToken);

            // 构建AI消息DTO，携带完整的群聊参数
            var aiMessageDto = new AiMessageDto
            {
                WxManagerId = wxManagerId,
                Content = callbackMessage.Data?.Content,
                MessageType = callbackMessage.MessageType,
                FromUser = callbackMessage.Data?.FromUser,
                ToUser = callbackMessage.Data?.ToUser,
                IsGroupMessage = true, // 群聊消息
                GroupId = callbackMessage.Data?.FromGroup,
                Context = new Dictionary<string, object>
                {
                    // 基础配置信息
                    ["GroupName"] = groupConfig.GroupName,
                    ["AiAgentId"] = "", // 从群配置获取
                    ["ProcessingId"] = processingId,
                    ["IsMentioned"] = groupConfig.IsMentioned,
                    
                    // 微信消息完整参数 - 群聊模式
                    ["WId"] = wxManager?.WId ?? "",
                    ["WcId"] = callbackMessage.Data?.FromGroup ?? "", // 群聊中WcId是群ID
                    ["Content"] = callbackMessage.Data?.Content ?? "",
                    ["FromGroup"] = callbackMessage.Data?.FromGroup ?? "",
                    ["FromGroupUser"] = callbackMessage.Data?.FromUser ?? "",
                    ["FromGroupUserNickName"] = senderInfo.NickName,
                    
                    // 原始回调数据
                    ["OriginalCallback"] = callbackMessage
                }
            };

            // 调用AI服务
            var response = await _aiService.ProcessMessageAsync(aiMessageDto, cancellationToken);

            if (response == null || string.IsNullOrEmpty(response.Content))
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 群聊AI处理返回空结果 - 群组: {GroupName}",
                    processingId, groupConfig.GroupName);
                return (false, "AI处理返回空结果", null);
            }

            _logger.LogDebug("[{ProcessingId}] ✅ 群聊AI处理成功 - 群组: {GroupName}, ResponseLength: {Length}",
                processingId, groupConfig.GroupName, response.Content.Length);

            return (true, null, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 群聊AI处理异常 - 群组: {GroupName}",
                processingId, groupConfig.GroupName);
            return (false, $"AI处理异常: {ex.Message}", null);
        }
    }

    /// <summary>
    /// 发送群聊回复（含@逻辑）
    /// </summary>
    private async Task<(bool Success, string? ErrorMessage)> SendGroupReplyWithAtAsync(
        WxCallbackMessageDto callbackMessage,
        AiMessageDto aiResponse,
        (string NickName, string WcId) senderInfo,
        bool shouldAtSender,
        string processingId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 获取WxManager信息
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var wxManager = await _dbContext.WxManagerEntities
                .Where(w => w.Id == wxManagerId)
                .Select(w => new { w.WId })
                .FirstOrDefaultAsync(cancellationToken);

            if (wxManager == null)
            {
                return (false, $"未找到WxManager - WxManagerId: {wxManagerId}");
            }

            // 构建发送命令
            var sendCommand = new WxSendTextMessageCommand
            {
                WId = wxManager.WId,
                WcId = callbackMessage.Data?.FromGroup!, // 群聊发送到群
                Content = aiResponse.Content!,
                FromGroup = callbackMessage.Data?.FromGroup,
                FromGroupUser = callbackMessage.Data?.FromUser,
                FromGroupUserNickName = senderInfo.NickName
            };

            // 群聊@逻辑：根据消息类型和触发条件决定是否@
            if (shouldAtSender)
            {
                sendCommand.At = senderInfo.WcId;
                _logger.LogDebug("[{ProcessingId}] 🏷️ 群聊@回复 - AtUser: {AtUser}, NickName: {NickName}",
                    processingId, senderInfo.WcId, senderInfo.NickName);
            }

            // 发送消息
            var result = await _wxMessageService.SendTextMessageAsync(Guid.Empty, sendCommand);

            if (result.IsSuccess)
            {
                _logger.LogInformation("[{ProcessingId}] 🚀 发送wx文本消息成功 - WId: {WId}, WcId: {WcId}, Content: {Content}, FromGroup: {FromGroup}, FromGroupUser: {FromGroupUser}, FromGroupUserNickName: {FromGroupUserNickName}",
                    processingId, sendCommand.WId, sendCommand.WcId,
                    sendCommand.Content?.Substring(0, Math.Min(50, sendCommand.Content?.Length ?? 0)),
                    sendCommand.FromGroup, sendCommand.FromGroupUser, sendCommand.FromGroupUserNickName);
                return (true, null);
            }
            else
            {
                _logger.LogError("[{ProcessingId}] ❌ 发送wx文本消息失败 - Error: {Error}",
                    processingId, result.Message);
                return (false, result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 发送群聊回复异常", processingId);
            return (false, $"发送异常: {ex.Message}");
        }
    }
}