using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.AiConfig;
using HappyWechat.Application.DTOs.MessageQueue;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.Options;
using HappyWechat.Domain.Entities;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.MessageProcessing;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using HappyWechat.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// AI处理服务 - 负责AI相关的处理逻辑
/// </summary>
public class AIProcessingService : IAIProcessingService
{
    private readonly ILogger<AIProcessingService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ApplicationDbContext _dbContext;
    private readonly IRetryStrategyService _retryStrategyService;

    public AIProcessingService(
        ILogger<AIProcessingService> logger,
        IServiceProvider serviceProvider,
        ApplicationDbContext dbContext,
        IRetryStrategyService retryStrategyService)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _dbContext = dbContext;
        _retryStrategyService = retryStrategyService;
    }

    /// <summary>
    /// 执行AI处理
    /// </summary>
    public async Task<MessageProcessingResult> ProcessWithAiAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, 
        WxMangerEntity wxManager, 
        string processingId, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var processingStats = new AiProcessingStats
        {
            ProcessingId = processingId,
            WxManagerId = wxManager.Id,
            FromUser = callbackMessage.Data?.FromUser ?? string.Empty,
            StartTime = DateTime.UtcNow
        };

        try
        {
            _logger.LogInformation("[{ProcessingId}] 开始AI处理 - FromUser: {FromUser}, MessageType: {MessageType}", 
                processingId, callbackMessage.Data?.FromUser, callbackMessage.Data?.MsgType);

            // 1. 检查AI配置
            var aiConfig = await GetAiConfigurationAsync(callbackMessage, wxManager, processingId);
            if (aiConfig == null)
            {
                var result = MessageProcessingResult.Skipped("未找到AI配置", $"ProcessingId: {processingId}");
                result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                return result;
            }

            processingStats.AiAgentId = aiConfig.AiAgentId.ToString();

            // 2. 检查AI处理条件
            var conditionCheck = await CheckAiProcessingConditionsAsync(callbackMessage, aiConfig, processingId);
            if (!conditionCheck.ShouldProcess)
            {
                var result = MessageProcessingResult.Skipped(conditionCheck.Reason, $"ProcessingId: {processingId}");
                result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                return result;
            }

            // 3. 构建AI处理消息
            var aiMessage = await BuildAiProcessMessageAsync(callbackMessage, wxManager, aiConfig, processingId);
            
            // 4. 执行AI处理
            var aiResult = await ExecuteAiProcessingAsync(aiMessage, processingStats, cancellationToken);

            // 5. 处理AI响应
            if (aiResult.Success)
            {
                var responseResult = await ProcessAiResponseAsync(aiResult.ResponseMessage!, processingStats, cancellationToken);
                responseResult.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                return responseResult;
            }
            else
            {
                var result = MessageProcessingResult.Failed(aiResult.ErrorMessage!, $"ProcessingId: {processingId}");
                result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                return result;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] AI处理异常", processingId);
            processingStats.ErrorMessage = ex.Message;
            await RecordProcessingStatsAsync(processingStats);
            
            return MessageProcessingResult.Failed($"AI处理异常: {ex.Message}", $"ProcessingId: {processingId}");
        }
        finally
        {
            processingStats.EndTime = DateTime.UtcNow;
            processingStats.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            await RecordProcessingStatsAsync(processingStats);
        }
    }

    /// <summary>
    /// 获取AI配置
    /// </summary>
    private async Task<AiConfigurationInfo?> GetAiConfigurationAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, 
        WxMangerEntity wxManager, 
        string processingId)
    {
        try
        {
            var aiConfigService = _serviceProvider.GetRequiredService<IAiConfigService>();
            
            // 判断是群组消息还是私聊消息
            if (!string.IsNullOrWhiteSpace(callbackMessage.Data?.FromGroup))
            {
                // 群组消息
                var groupConfig = await aiConfigService.GetGroupAiConfigAsync(
                    callbackMessage.Data.FromGroup, 
                    wxManager.Id);
                
                if (groupConfig?.IsEnabled == true && groupConfig.AiAgentId.HasValue)
                {
                    return new AiConfigurationInfo
                    {
                        AiAgentId = groupConfig.AiAgentId.Value,
                        CustomPrompt = groupConfig.CustomPrompt,
                        IsGroupMessage = true,
                        GroupId = callbackMessage.Data.FromGroup,
                        OnlyReplyWhenMentioned = groupConfig.OnlyReplyWhenMentioned,
                        IsEnabled = groupConfig.IsEnabled
                    };
                }
            }
            else
            {
                // 私聊消息
                var contactConfig = await aiConfigService.GetContactAiConfigAsync(
                    callbackMessage.Data?.FromUser ?? string.Empty, 
                    wxManager.Id);
                
                if (contactConfig?.IsEnabled == true && contactConfig.AiAgentId.HasValue)
                {
                    return new AiConfigurationInfo
                    {
                        AiAgentId = contactConfig.AiAgentId.Value,
                        CustomPrompt = contactConfig.CustomPrompt,
                        IsGroupMessage = false,
                        ContactId = callbackMessage.Data?.FromUser ?? string.Empty,
                        IsEnabled = contactConfig.IsEnabled
                    };
                }
            }

            _logger.LogDebug("[{ProcessingId}] 未找到有效的AI配置", processingId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 获取AI配置异常", processingId);
            return null;
        }
    }

    /// <summary>
    /// 检查AI处理条件
    /// </summary>
    private async Task<AiProcessingConditionResult> CheckAiProcessingConditionsAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, 
        AiConfigurationInfo aiConfig, 
        string processingId)
    {
        try
        {
            // 1. 检查是否启用AI
            if (!aiConfig.IsEnabled)
            {
                return new AiProcessingConditionResult
                {
                    ShouldProcess = false,
                    Reason = "AI处理未启用"
                };
            }

            // 2. 检查群组@逻辑
            if (aiConfig.IsGroupMessage && aiConfig.OnlyReplyWhenMentioned)
            {
                var atCheckResult = await CheckGroupAtLogicAsync(callbackMessage, processingId);
                if (!atCheckResult.ShouldReply)
                {
                    return new AiProcessingConditionResult
                    {
                        ShouldProcess = false,
                        Reason = atCheckResult.Reason
                    };
                }
            }

            // 3. 检查消息内容
            if (callbackMessage.Data?.MsgType == 1 && 
                string.IsNullOrWhiteSpace(callbackMessage.Data?.Content))
            {
                return new AiProcessingConditionResult
                {
                    ShouldProcess = false,
                    Reason = "消息内容为空"
                };
            }

            // 4. 检查全局群组回复设置
            if (aiConfig.IsGroupMessage)
            {
                var globalSettingsCheck = await CheckGlobalGroupReplySettingsAsync(callbackMessage, processingId);
                if (!globalSettingsCheck.AllowReply)
                {
                    return new AiProcessingConditionResult
                    {
                        ShouldProcess = false,
                        Reason = globalSettingsCheck.Reason
                    };
                }
            }

            return new AiProcessingConditionResult
            {
                ShouldProcess = true,
                Reason = "满足AI处理条件"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 检查AI处理条件异常", processingId);
            return new AiProcessingConditionResult
            {
                ShouldProcess = false,
                Reason = $"检查条件异常: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 检查群组@逻辑
    /// </summary>
    private async Task<GroupAtCheckResult> CheckGroupAtLogicAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, 
        string processingId)
    {
        try
        {
            var content = callbackMessage.Data?.Content ?? string.Empty;
            
            // 检查是否包含@
            if (!content.Contains("@"))
            {
                return new GroupAtCheckResult
                {
                    ShouldReply = false,
                    Reason = "群组消息需要@才能触发回复"
                };
            }

            // 检查是否@了机器人
            var wxManager = await _dbContext.WxMangerEntities
                .FirstOrDefaultAsync(w => w.WId == callbackMessage.Data!.WId);
            
            if (wxManager == null)
            {
                return new GroupAtCheckResult
                {
                    ShouldReply = false,
                    Reason = "找不到微信管理器"
                };
            }

            // 简单检查是否@了机器人（这里可以根据实际需求完善）
            if (content.Contains($"@{wxManager.NickName}") || 
                content.Contains($"@{wxManager.WId}"))
            {
                return new GroupAtCheckResult
                {
                    ShouldReply = true,
                    Reason = "检测到@机器人"
                };
            }

            return new GroupAtCheckResult
            {
                ShouldReply = false,
                Reason = "未检测到@机器人"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 检查群组@逻辑异常", processingId);
            return new GroupAtCheckResult
            {
                ShouldReply = false,
                Reason = $"检查@逻辑异常: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 检查全局群组回复设置
    /// </summary>
    private async Task<GlobalGroupReplyResult> CheckGlobalGroupReplySettingsAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, 
        string processingId)
    {
        try
        {
            var systemConfigManager = _serviceProvider.GetRequiredService<ISystemConfigManager>();
            var globalSettings = await systemConfigManager.GetGroupManagementConfigAsync();
            
            if (!globalSettings.EnableGroupManagement)
            {
                return new GlobalGroupReplyResult
                {
                    AllowReply = false,
                    Reason = "全局设置禁止群组管理功能"
                };
            }

            return new GlobalGroupReplyResult
            {
                AllowReply = true,
                Reason = "全局设置允许群组回复"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 检查全局群组回复设置异常", processingId);
            return new GlobalGroupReplyResult
            {
                AllowReply = true, // 异常情况下默认允许
                Reason = $"检查全局设置异常: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 构建AI处理消息
    /// </summary>
    private async Task<AiProcessMessage> BuildAiProcessMessageAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, 
        WxMangerEntity wxManager, 
        AiConfigurationInfo aiConfig, 
        string processingId)
    {
        var aiMessage = new AiProcessMessage
        {
            OriginalMessageId = Guid.NewGuid().ToString(),
            WxManagerId = wxManager.Id.ToString(),
            UserId = wxManager.UserId.ToString(),
            FromUser = callbackMessage.Data?.FromUser ?? string.Empty,
            IsGroupMessage = aiConfig.IsGroupMessage,
            GroupId = aiConfig.GroupId,
            Content = callbackMessage.Data?.Content ?? string.Empty,
            FileUrls = new List<string>(),
            CreatedAt = DateTime.UtcNow
        };

        // 如果是非文本消息，处理文件URL
        if (callbackMessage.Data?.MsgType != 1)
        {
            var fileUrls = await ExtractFileUrlsAsync(callbackMessage, processingId);
            aiMessage.FileUrls = fileUrls;
        }

        return aiMessage;
    }

    /// <summary>
    /// 提取文件URL
    /// </summary>
    private async Task<List<string>> ExtractFileUrlsAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage, 
        string processingId)
    {
        try
        {
            var fileUrls = new List<string>();
            
            // 根据消息类型提取文件URL
            if (callbackMessage.Data?.MsgType == 3) // 图片
            {
                // 这里需要根据实际的消息结构来提取图片URL
                // 暂时返回空列表
            }
            else if (callbackMessage.Data?.MsgType == 34) // 语音
            {
                // 提取语音文件URL
            }
            else if (callbackMessage.Data?.MsgType == 43) // 视频
            {
                // 提取视频文件URL
            }
            
            return fileUrls;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 提取文件URL异常", processingId);
            return new List<string>();
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 执行AI处理
    /// </summary>
    private async Task<AiProcessingResult> ExecuteAiProcessingAsync(
        AiProcessMessage aiMessage, 
        AiProcessingStats stats, 
        CancellationToken cancellationToken)
    {
        try
        {
            var aiAgentService = _serviceProvider.GetRequiredService<IAiAgentService>();
            
            // 使用重试策略调用AI服务
            var retryPolicy = new RetryPolicy 
            { 
                MaxRetryAttempts = 3,
                BaseDelay = TimeSpan.FromSeconds(1),
                DelayMultiplier = 2.0
            };
            
            var response = await _retryStrategyService.ExecuteWithRetryAsync(
                async () => await CallAiServiceAsync(aiMessage, aiAgentService, stats.ProcessingId, stats.AiAgentId),
                retryPolicy,
                cancellationToken);

            if (response != null)
            {
                stats.AiProvider = response.AiProvider;
                stats.AiResponseLength = response.Content?.Length ?? 0;
                
                return new AiProcessingResult
                {
                    Success = true,
                    ResponseMessage = response
                };
            }
            else
            {
                return new AiProcessingResult
                {
                    Success = false,
                    ErrorMessage = "AI服务返回空响应"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 执行AI处理异常", stats.ProcessingId);
            return new AiProcessingResult
            {
                Success = false,
                ErrorMessage = $"AI处理异常: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 调用AI服务
    /// </summary>
    private async Task<AiResponseMessage?> CallAiServiceAsync(
        AiProcessMessage aiMessage, 
        IAiAgentService aiAgentService, 
        string processingId,
        string? aiAgentId)
    {
        try
        {
            var aiAgent = await aiAgentService.GetByIdAsync(Guid.Parse(aiMessage.UserId), Guid.Parse(aiAgentId ?? Guid.Empty.ToString()));
            if (aiAgent == null)
            {
                _logger.LogError("[{ProcessingId}] AI智能体不存在", processingId);
                return null;
            }

            var prompt = BuildPrompt(aiMessage);
            var response = await aiAgentService.ProcessMessageAsync(aiAgent.Id, prompt, new { Context = aiMessage });

            if (string.IsNullOrWhiteSpace(response))
            {
                _logger.LogWarning("[{ProcessingId}] AI返回空响应", processingId);
                return null;
            }

            return new AiResponseMessage
            {
                OriginalMessageId = aiMessage.OriginalMessageId,
                WxManagerId = aiMessage.WxManagerId,
                WId = aiMessage.FromUser,
                ToUser = aiMessage.IsGroupMessage ? aiMessage.GroupId! : aiMessage.FromUser,
                Content = response,
                AiProvider = aiAgent.ProviderType.ToString(),
                GeneratedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 调用AI服务异常", processingId);
            throw;
        }
    }

    /// <summary>
    /// 构建提示词
    /// </summary>
    private string BuildPrompt(AiProcessMessage aiMessage)
    {
        var prompt = aiMessage.Content;
        
        // CustomPrompt is handled through aiConfig, not aiMessage

        if (aiMessage.FileUrls?.Any() == true)
        {
            var fileList = string.Join("\n", aiMessage.FileUrls.Select((url, index) => $"文件{index + 1}: {url}"));
            prompt = $"{prompt}\n\n附件:\n{fileList}";
        }

        if (aiMessage.IsGroupMessage)
        {
            var groupPrefix = !string.IsNullOrWhiteSpace(aiMessage.GroupId) ? $"[群聊: {aiMessage.GroupId}]" : "[群聊消息]";
            prompt = $"{groupPrefix} {prompt}";
        }

        return prompt;
    }

    /// <summary>
    /// 处理AI响应
    /// </summary>
    private async Task<MessageProcessingResult> ProcessAiResponseAsync(
        AiResponseMessage responseMessage, 
        AiProcessingStats stats, 
        CancellationToken cancellationToken)
    {
        try
        {
            var aiResponseProcessor = _serviceProvider.GetRequiredService<IEnhancedAiResponseProcessor>();
            var result = await aiResponseProcessor.ProcessAiResponseAsync(responseMessage);
            var success = !string.IsNullOrEmpty(result); // 如果返回了处理ID，则认为成功

            if (success)
            {
                stats.Success = true;
                return new MessageProcessingResult
                {
                    Success = true,
                    AiResponse = "AI响应处理成功",
                    ProcessingDetails = $"ProcessingId: {stats.ProcessingId}"
                };
            }
            else
            {
                return new MessageProcessingResult
                {
                    Success = false,
                    ErrorMessage = "AI响应处理失败",
                    ProcessingDetails = $"ProcessingId: {stats.ProcessingId}"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理AI响应异常", stats.ProcessingId);
            return MessageProcessingResult.Failed($"AI响应处理异常: {ex.Message}", $"ProcessingId: {stats.ProcessingId}");
        }
    }

    /// <summary>
    /// 记录处理统计信息
    /// </summary>
    private async Task RecordProcessingStatsAsync(AiProcessingStats stats)
    {
        try
        {
            // 这里可以将统计信息保存到数据库或发送到监控系统
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 记录处理统计异常", stats.ProcessingId);
        }
    }
}

/// <summary>
/// AI配置信息
/// </summary>
public class AiConfigurationInfo
{
    public Guid AiAgentId { get; set; }
    public string? CustomPrompt { get; set; }
    public bool IsGroupMessage { get; set; }
    public string? GroupId { get; set; }
    public string? ContactId { get; set; }
    public bool OnlyReplyWhenMentioned { get; set; }
    public bool IsEnabled { get; set; }
}

/// <summary>
/// AI处理条件结果
/// </summary>
public class AiProcessingConditionResult
{
    public bool ShouldProcess { get; set; }
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 群组@检查结果
/// </summary>
public class GroupAtCheckResult
{
    public bool ShouldReply { get; set; }
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 全局群组回复结果
/// </summary>
public class GlobalGroupReplyResult
{
    public bool AllowReply { get; set; }
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// AI处理结果
/// </summary>
public class AiProcessingResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public AiResponseMessage? ResponseMessage { get; set; }
}

/// <summary>
/// AI处理统计信息
/// </summary>
public class AiProcessingStats
{
    public string ProcessingId { get; set; } = string.Empty;
    public Guid WxManagerId { get; set; }
    public string FromUser { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? AiAgentId { get; set; }
    public string? AiProvider { get; set; }
    public int AiResponseLength { get; set; }
    public long ProcessingTimeMs { get; set; }
}