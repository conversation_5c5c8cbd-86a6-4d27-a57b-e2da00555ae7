using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.MessageProcess;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 媒体处理结果
/// </summary>
public class MediaProcessResult
{
    public bool IsSuccess { get; set; }
    public string? LocalPath { get; set; }
    public string? StorageUrl { get; set; }
    public string? PublicUrl { get; set; }
    public string? FileName { get; set; }
    public long FileSize { get; set; }
    public string? ContentType { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 媒体消息处理器接口
/// </summary>
public interface IMediaMessageProcessor
{
    /// <summary>
    /// 处理图片消息
    /// </summary>
    Task<MediaProcessResult> ProcessImageMessageAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 处理语音消息
    /// </summary>
    Task<MediaProcessResult> ProcessVoiceMessageAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 处理文件消息
    /// </summary>
    Task<MediaProcessResult> ProcessFileMessageAsync(WxCallbackMessageDto callbackMessage, string processingId);
    
    /// <summary>
    /// 检查是否需要处理媒体
    /// </summary>
    bool ShouldProcessMedia(string messageType);
}

/// <summary>
/// 媒体消息处理器实现
/// </summary>
public class MediaMessageProcessor : IMediaMessageProcessor
{
    private readonly HappyWechat.Infrastructure.MediaProcessing.IUnifiedMediaProcessor _unifiedMediaProcessor;
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<MediaMessageProcessor> _logger;

    public MediaMessageProcessor(
        HappyWechat.Infrastructure.MediaProcessing.IUnifiedMediaProcessor unifiedMediaProcessor,
        IFileStorageService fileStorageService,
        ILogger<MediaMessageProcessor> logger)
    {
        _unifiedMediaProcessor = unifiedMediaProcessor;
        _fileStorageService = fileStorageService;
        _logger = logger;
    }

    public async Task<MediaProcessResult> ProcessImageMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 开始处理图片消息 - WcId: {WcId}, FromUser: {FromUser}", 
                processingId, callbackMessage.WcId, callbackMessage.Data?.FromUser);

            // 使用统一媒体处理器处理图片
            var mediaResult = await _unifiedMediaProcessor.ProcessMediaAsync(callbackMessage);
            
            if (!mediaResult.Success)
            {
                return new MediaProcessResult
                {
                    IsSuccess = false,
                    ErrorMessage = mediaResult.ErrorMessage ?? "图片处理失败"
                };
            }

            return new MediaProcessResult
            {
                IsSuccess = true,
                LocalPath = mediaResult.LocalFilePath,
                PublicUrl = mediaResult.PublicUrl,
                FileName = mediaResult.FileName,
                FileSize = mediaResult.FileSize,
                Metadata =
                {
                    ["MessageType"] = callbackMessage.MessageType,
                    ["MediaType"] = mediaResult.MediaType,
                    ["ProcessingId"] = processingId
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理图片消息失败", processingId);
            return new MediaProcessResult
            {
                IsSuccess = false,
                ErrorMessage = $"处理图片消息失败: {ex.Message}"
            };
        }
    }

    public async Task<MediaProcessResult> ProcessVoiceMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 开始处理语音消息 - WcId: {WcId}, FromUser: {FromUser}", 
                processingId, callbackMessage.WcId, callbackMessage.Data?.FromUser);

            // 使用统一媒体处理器处理语音
            var mediaResult = await _unifiedMediaProcessor.ProcessMediaAsync(callbackMessage);

            if (!mediaResult.Success)
            {
                return new MediaProcessResult
                {
                    IsSuccess = false,
                    ErrorMessage = mediaResult.ErrorMessage ?? "语音处理失败"
                };
            }

            // TODO: 实现silk格式转换MP3
            // 这里可以集成音频转换服务
            
            return new MediaProcessResult
            {
                IsSuccess = true,
                LocalPath = mediaResult.LocalFilePath,
                PublicUrl = mediaResult.PublicUrl,
                FileName = mediaResult.FileName,
                FileSize = mediaResult.FileSize,
                Metadata =
                {
                    ["MessageType"] = callbackMessage.MessageType,
                    ["MediaType"] = mediaResult.MediaType,
                    ["OriginalFormat"] = "silk",
                    ["ProcessingId"] = processingId
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理语音消息失败", processingId);
            return new MediaProcessResult
            {
                IsSuccess = false,
                ErrorMessage = $"处理语音消息失败: {ex.Message}"
            };
        }
    }

    public async Task<MediaProcessResult> ProcessFileMessageAsync(WxCallbackMessageDto callbackMessage, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 开始处理文件消息 - WcId: {WcId}, FromUser: {FromUser}", 
                processingId, callbackMessage.WcId, callbackMessage.Data?.FromUser);

            // 使用统一媒体处理器处理文件
            var mediaResult = await _unifiedMediaProcessor.ProcessMediaAsync(callbackMessage);

            if (!mediaResult.Success)
            {
                return new MediaProcessResult
                {
                    IsSuccess = false,
                    ErrorMessage = mediaResult.ErrorMessage ?? "文件处理失败"
                };
            }

            return new MediaProcessResult
            {
                IsSuccess = true,
                LocalPath = mediaResult.LocalFilePath,
                PublicUrl = mediaResult.PublicUrl,
                FileName = mediaResult.FileName,
                FileSize = mediaResult.FileSize,
                Metadata =
                {
                    ["MessageType"] = callbackMessage.MessageType,
                    ["MediaType"] = mediaResult.MediaType,
                    ["ProcessingId"] = processingId
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 处理文件消息失败", processingId);
            return new MediaProcessResult
            {
                IsSuccess = false,
                ErrorMessage = $"处理文件消息失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 🔧 重构：判断是否需要处理媒体文件 - 使用60009/80009替代60008/80008
    /// </summary>
    public bool ShouldProcessMedia(string messageType)
    {
        return messageType switch
        {
            "60002" or "80002" => true, // 图片消息
            "60004" or "80004" => true, // 语音消息
            "60009" or "80009" => true, // 文件发送完成消息
            _ => false
        };
    }


}
