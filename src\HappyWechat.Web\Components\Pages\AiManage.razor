@page "/ai-manage"
@rendermode InteractiveServer
@attribute [Authorize]
@using Microsoft.AspNetCore.Authorization
@using MudBlazor
@using HappyWechat.Application.Commons
@using HappyWechat.Application.DTOs.AiAgent
@using HappyWechat.Application.DTOs.SystemConfig
@using HappyWechat.Application.Interfaces
@using HappyWechat.Domain.ValueObjects.Enums
@using HappyWechat.Infrastructure.Configuration
@using HappyWechat.Web.Components.AiAgent
@using HappyWechat.Web.Components.Common
@using HappyWechat.Web.FrontApis
@using Microsoft.Extensions.DependencyInjection
@using System.Text.Json
@inherits BaseConfigSyncComponent
@inject AiAgentApi AiAgentApi
@inject IGlobalAiConfigService GlobalAiConfigService
@inject IAiConfigHotReloadService HotReloadService
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@inject ISystemConfigManager SystemConfigManager
@inject IServiceProvider ServiceProvider
@inject ILogger<AiManage> Logger

<MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
    <!-- AI管理标签页 -->
    <MudTabs Elevation="2" Rounded="true" ActivePanelIndex="@_activeMainTabIndex" Class="mb-4"
             Color="Color.Primary" TabPanelClass="pa-0" ActivePanelIndexChanged="@OnMainTabChanged">
        <MudTabPanel Text="智能体管理" Icon="@Icons.Material.Filled.Psychology">
            <MudPaper Elevation="1" Class="pa-4">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Psychology" Class="mr-2" />
                    AI智能体管理
                </MudText>

                <MudTable Items="@Elements" Hover="true" Striped="true" @bind-SelectedItem="selectedItem"
                  Loading="_isLoading" LoadingProgressColor="Color.Info" Elevation="0"
                  ServerData="@(new Func<TableState, CancellationToken, Task<TableData<AiAgentDto?>>>(ServerReload))"
                  @ref="_table">
    <ToolBarContent>
        <MudGrid Class="align-center" Spacing="2">
            <MudItem xs="12" sm="6" md="3">
                <MudTextField @bind-Value="_searchKeyword" Placeholder="搜索名称或描述"
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"
                              Variant="Variant.Outlined"
                              Class="mt-0"></MudTextField>
            </MudItem>
            <MudItem xs="12" sm="6" md="2">
                <MudSelect T="AiProviderType?" @bind-Value="_selectedProviderType" Label="提供商"
                           Clearable="true" Variant="Variant.Outlined">
                    <MudSelectItem T="AiProviderType?" Value="@((AiProviderType?)null)">全部</MudSelectItem>
                    <MudSelectItem T="AiProviderType?" Value="AiProviderType.CoZe">扣子</MudSelectItem>
                    <MudSelectItem T="AiProviderType?" Value="AiProviderType.MaxKB">MaxKB</MudSelectItem>
                    <MudSelectItem T="AiProviderType?" Value="AiProviderType.Dify">Dify</MudSelectItem>
                    <MudSelectItem T="AiProviderType?" Value="AiProviderType.ChatGPT">ChatGPT</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="2">
                <MudSelect T="bool?" @bind-Value="_selectedEnabledStatus" Label="状态"
                           Clearable="true" Variant="Variant.Outlined">
                    <MudSelectItem T="bool?" Value="@((bool?)null)">全部</MudSelectItem>
                    <MudSelectItem T="bool?" Value="true">已启用</MudSelectItem>
                    <MudSelectItem T="bool?" Value="false">已禁用</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="2" Class="d-flex align-center">
                <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="OnSearch"
                           Size="Size.Medium" FullWidth="true"
                           StartIcon="@Icons.Material.Filled.Search">
                    搜索
                </MudButton>
            </MudItem>
            <MudItem xs="12" md="3" Class="d-flex justify-end align-center gap-2">
                <MudButton Variant="Variant.Outlined" Color="Color.Secondary"
                           StartIcon="@Icons.Material.Filled.Refresh" OnClick="RefreshList"
                           Size="Size.Medium">
                    刷新
                </MudButton>
                <MudButton Variant="Variant.Filled" Color="Color.Success" OnClick="AddAgent"
                           StartIcon="@Icons.Material.Filled.Add" Size="Size.Medium">
                    添加智能体
                </MudButton>
            </MudItem>
        </MudGrid>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>名称</MudTh>
        <MudTh>提供商</MudTh>
        <MudTh>描述</MudTh>
        <MudTh>状态</MudTh>
        <MudTh>创建时间</MudTh>
        <MudTh>操作</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="名称">
            <MudText Typo="Typo.body1">@context.Name</MudText>
        </MudTd>
        <MudTd DataLabel="提供商">
            <MudChip T="string" Color="@GetProviderColor(context.ProviderType)" Size="Size.Small">
                @context.ProviderTypeName
            </MudChip>
        </MudTd>
        <MudTd DataLabel="描述">
            <MudText Typo="Typo.body2">@(context.Description ?? "无描述")</MudText>
        </MudTd>
        <MudTd DataLabel="状态">
            <MudSwitch T="bool"
                       @bind-Value="@context.IsEnabled"
                       @bind-Value:after="@(() => ToggleEnabledAsync(context))"
                       Color="Color.Primary" />
        </MudTd>
        <MudTd DataLabel="创建时间">
            <MudText Typo="Typo.body2">@context.CreatedAt.ToString("yyyy-MM-dd HH:mm")</MudText>
        </MudTd>
        <MudTd DataLabel="操作">
            <div class="d-flex gap-1">
                <MudButton Variant="Variant.Text" Color="Color.Info" Size="Size.Small"
                           StartIcon="@Icons.Material.Filled.PlayArrow"
                           OnClick="@(() => TestAgent(context))">
                    测试
                </MudButton>
                <MudButton Variant="Variant.Text" Color="Color.Primary" Size="Size.Small"
                           StartIcon="@Icons.Material.Filled.Edit"
                           OnClick="@(() => EditAgent(context))">
                    编辑
                </MudButton>
                <MudButton Variant="Variant.Text" Color="Color.Error" Size="Size.Small"
                           StartIcon="@Icons.Material.Filled.Delete"
                           OnClick="@(() => DeleteAgent(context))">
                    删除
                </MudButton>
            </div>
        </MudTd>
    </RowTemplate>
    <NoRecordsContent>
        <MudText>暂无智能体数据</MudText>
    </NoRecordsContent>
    <LoadingContent>
        <MudText>加载中...</MudText>
    </LoadingContent>
    <PagerContent>
        <MudTablePager RowsPerPageString="每页行数"
                       PageSizeOptions="new []{10,20,30,50}"
                       InfoFormat="{first_item}-{last_item} 共 {all_items} 条"
                       HorizontalAlignment="HorizontalAlignment.Right"/>
    </PagerContent>
</MudTable>
            </MudPaper>
        </MudTabPanel>

        <MudTabPanel Text="机器人配置" Icon="@Icons.Material.Filled.SmartToy">
            <MudPaper Elevation="1" Class="pa-4">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.SmartToy" Class="mr-2" />
                    机器人全局配置
                </MudText>

                @if (_isLoadingConfig)
                {
                    <MudProgressCircular Indeterminate="true" />
                }
                else
                {
                    <MudGrid>
                        <!-- 敏感词检测配置 -->
                        <MudItem xs="12">
                            <MudCard>
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">
                                            <MudIcon Icon="@Icons.Material.Filled.Security" Class="mr-2" />
                                            敏感词检测配置
                                        </MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <MudGrid>
                                        <MudItem xs="12" md="6">
                                            <MudSwitch T="bool"
                                                     @bind-Value="_eYunRiskControlConfig.EnableIncomingMessageSensitiveWordDetection"
                                                     Label="启用接收消息敏感词检测"
                                                     Color="Color.Primary"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudSwitch T="bool"
                                                     @bind-Value="_eYunRiskControlConfig.EnableAiReplyMessageSensitiveWordDetection"
                                                     Label="启用AI回复敏感词检测"
                                                     Color="Color.Primary"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudSelect T="SensitiveWordActionType" @bind-Value="_eYunRiskControlConfig.SensitiveWordAction"
                                                     Label="敏感词处理方式"
                                                     Variant="Variant.Outlined">
                                                <MudSelectItem Value="SensitiveWordActionType.Block">阻止发送</MudSelectItem>
                                                <MudSelectItem Value="SensitiveWordActionType.LogOnly">记录日志但允许</MudSelectItem>
                                                <MudSelectItem Value="SensitiveWordActionType.Replace">替换为星号</MudSelectItem>
                                            </MudSelect>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudNumericField T="int" @bind-Value="_eYunRiskControlConfig.MaxContentLength"
                                                           Label="单条消息文字最大长度"
                                                           Variant="Variant.Outlined"
                                                           Min="100"
                                                           Max="2000"
                                                           HelperText="默认: 1500, 严格限制2000以下"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudTextField @bind-Value="_sensitiveWordsText"
                                                        Label="通用敏感词列表"
                                                        Variant="Variant.Outlined"
                                                        Lines="4"
                                                        HelperText="通用敏感词，适用于接收消息和AI回复检测"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudTextField @bind-Value="_incomingMessageSensitiveWordsText"
                                                        Label="接收消息敏感词"
                                                        Variant="Variant.Outlined"
                                                        Lines="4"
                                                        HelperText="专门用于检测接收消息的敏感词"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudTextField @bind-Value="_aiReplySensitiveWordsText"
                                                        Label="AI回复敏感词"
                                                        Variant="Variant.Outlined"
                                                        Lines="4"
                                                        HelperText="专门用于检测AI回复内容的敏感词"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudSelect T="SensitiveWordActionType" @bind-Value="_eYunRiskControlConfig.IncomingMessageSensitiveWordAction"
                                                     Label="接收消息敏感词处理方式"
                                                     Variant="Variant.Outlined">
                                                <MudSelectItem Value="SensitiveWordActionType.LogOnly">记录日志但继续</MudSelectItem>
                                                <MudSelectItem Value="SensitiveWordActionType.Block">拦截不处理</MudSelectItem>
                                                <MudSelectItem Value="SensitiveWordActionType.Replace">替换为星号</MudSelectItem>
                                            </MudSelect>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudSelect T="SensitiveWordActionType" @bind-Value="_eYunRiskControlConfig.AIReplySensitiveWordAction"
                                                     Label="AI回复敏感词处理方式"
                                                     Variant="Variant.Outlined">
                                                <MudSelectItem Value="SensitiveWordActionType.Replace">替换为星号</MudSelectItem>
                                                <MudSelectItem Value="SensitiveWordActionType.Block">拦截不发送</MudSelectItem>
                                                <MudSelectItem Value="SensitiveWordActionType.LogOnly">记录日志但发送</MudSelectItem>
                                            </MudSelect>
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- 自动通过好友配置 -->
                        <MudItem xs="12">
                            <MudCard>
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">
                                            <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Class="mr-2" />
                                            自动通过好友配置
                                        </MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <MudGrid>
                                        <MudItem xs="12" md="6">
                                            <MudSwitch T="bool"
                                                     @bind-Value="_eYunRiskControlConfig.EnableAutoAcceptFriends"
                                                     Label="启用自动通过好友功能"
                                                     Color="Color.Primary"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudSwitch T="bool"
                                                     @bind-Value="_eYunRiskControlConfig.EnableKeywordMatching"
                                                     Label="启用关键词匹配模式"
                                                     Color="Color.Primary"
                                                     HelperText="开启后只有包含关键词的请求才会通过"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudNumericField T="int" @bind-Value="_eYunRiskControlConfig.AutoAcceptFriendsPerDay"
                                                           Label="每日通过好友人数限制"
                                                           Variant="Variant.Outlined"
                                                           Min="50"
                                                           Max="200"
                                                           HelperText="默认: 120, 严格限制200以下"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudNumericField T="int" @bind-Value="_eYunRiskControlConfig.WelcomeMessageDelaySeconds"
                                                           Label="欢迎语发送延迟(秒)"
                                                           Variant="Variant.Outlined"
                                                           Min="5"
                                                           Max="60"
                                                           HelperText="默认: 10秒"/>
                                        </MudItem>
                                        <MudItem xs="12">
                                            <MudTextField @bind-Value="_autoAcceptKeywordsText"
                                                        Label="通过好友关键词列表"
                                                        Variant="Variant.Outlined"
                                                        Lines="2"
                                                        HelperText="每行一个关键词，包含任一关键词则通过"/>
                                        </MudItem>
                                        <MudItem xs="12">
                                            <MudTextField @bind-Value="_eYunRiskControlConfig.FriendWelcomeMessage"
                                                        Label="通过好友欢迎语配置"
                                                        Variant="Variant.Outlined"
                                                        Lines="3"
                                                        HelperText="成功添加好友后的欢迎消息"/>
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>



                        <!-- 消息长度检查配置 -->
                        <MudItem xs="12">
                            <MudCard>
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">
                                            <MudIcon Icon="@Icons.Material.Filled.TextFields" Class="mr-2" />
                                            消息长度检查配置
                                        </MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <MudGrid>
                                        <MudItem xs="12" md="6">
                                            <MudSwitch T="bool"
                                                     @bind-Value="_eYunRiskControlConfig.EnableMessageLengthCheck"
                                                     Label="启用消息长度检查"
                                                     Color="Color.Primary"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudNumericField T="int" @bind-Value="_eYunRiskControlConfig.MessageLengthLimit"
                                                           Label="消息长度限制（字符数）"
                                                           Variant="Variant.Outlined"
                                                           Min="100"
                                                           Max="5000"
                                                           HelperText="默认: 1000, 超过此长度的消息将自动分割"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudNumericField T="int" @bind-Value="_eYunRiskControlConfig.DailyAutoAcceptFriendLimit"
                                                           Label="每日自动通过好友限制"
                                                           Variant="Variant.Outlined"
                                                           Min="1"
                                                           Max="100"
                                                           HelperText="默认: 10, 每日自动通过好友的数量限制"/>
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudTextField @bind-Value="_eYunRiskControlConfig.AutoAcceptFriendWelcomeMessage"
                                                        Label="自动通过好友后欢迎语"
                                                        Variant="Variant.Outlined"
                                                        Lines="2"
                                                        HelperText="自动通过好友请求后发送的欢迎消息"/>
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- 保存按钮 -->
                        <MudItem xs="12" Class="d-flex justify-end">
                            <MudButton Variant="Variant.Filled" Color="Color.Primary"
                                     OnClick="@SaveRobotConfig" Disabled="_isSavingConfig"
                                     StartIcon="@Icons.Material.Filled.Save">
                                @if (_isSavingConfig)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                    <span class="ml-2">保存中...</span>
                                }
                                else
                                {
                                    <span>保存配置</span>
                                }
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                }
            </MudPaper>
        </MudTabPanel>
    </MudTabs>
</MudContainer>

@code {
    // 主标签页索引
    private int _activeMainTabIndex = 0; // 默认显示智能体管理标签页

    // 表格引用
    private MudTable<AiAgentDto> _table = null!;

    private List<AiAgentDto> Elements = new();
    private AiAgentDto? selectedItem;
    private bool _isLoading = false;
    private string _searchKeyword = "";
    private AiProviderType? _selectedProviderType;
    private bool? _selectedEnabledStatus;
    private int _currentPage = 1;
    private int _pageSize = 10;
    private int _totalPages = 1;

    // 机器人配置相关
    private bool _isLoadingConfig = true;
    private bool _isSavingConfig = false;
    private EYunRiskControlConfigDto _eYunRiskControlConfig = new();
    
    // 文本字段
    private string _sensitiveWordsText = string.Empty;
    private string _autoAcceptKeywordsText = string.Empty;
    private string _incomingMessageSensitiveWordsText = string.Empty;
    private string _aiReplySensitiveWordsText = string.Empty;


    protected override async Task OnInitializedAsync()
    {
        // MudTable with ServerData automatically loads data when initialized
        // No manual reload needed here

        // 加载机器人配置
        await LoadRobotConfig();
    }

    /// <summary>
    /// 主标签页切换事件处理
    /// </summary>
    private async Task OnMainTabChanged(int newIndex)
    {
        _activeMainTabIndex = newIndex;

        // 当切换到机器人配置标签页时，重新加载配置
        if (newIndex == 1) // 机器人配置是第二个标签页（索引为1）
        {
            Logger.LogInformation("切换到机器人配置标签页，重新加载配置");
            await LoadRobotConfig();
        }

        StateHasChanged();
    }

    private async Task LoadAgents()
    {
        _isLoading = true;
        try
        {
            var query = new AiAgentQuery
            {
                PageQuery = new PageQuery { Page = _currentPage, PageSize = _pageSize },
                SearchKeyword = string.IsNullOrWhiteSpace(_searchKeyword) ? null : _searchKeyword,
                ProviderType = _selectedProviderType,
                IsEnabled = _selectedEnabledStatus
            };

            var result = await AiAgentApi.GetPagedListAsync(query);
            if (result != null)
            {
                Elements = result.Items;
                _totalPages = result.TotalPages;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task<TableData<AiAgentDto?>> ServerReload(TableState state, CancellationToken cancellationToken)
    {
        try
        {
            _isLoading = true;

            // 更新分页参数
            var currentPage = state.Page + 1; // TableState.Page 是从0开始的
            var pageSize = state.PageSize;

            var query = new AiAgentQuery
            {
                PageQuery = new PageQuery { Page = currentPage, PageSize = pageSize },
                SearchKeyword = string.IsNullOrWhiteSpace(_searchKeyword) ? null : _searchKeyword,
                ProviderType = _selectedProviderType,
                IsEnabled = _selectedEnabledStatus
            };

            var result = await AiAgentApi.GetPagedListAsync(query);
            if (result != null)
            {
                Elements = result.Items;
                _totalPages = result.TotalPages;

                return new TableData<AiAgentDto?>
                {
                    TotalItems = result.TotalCount,
                    Items = result.Items.Cast<AiAgentDto?>()
                };
            }

            return new TableData<AiAgentDto?> { TotalItems = 0, Items = new List<AiAgentDto?>() };
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载失败: {ex.Message}", Severity.Error);
            return new TableData<AiAgentDto?> { TotalItems = 0, Items = new List<AiAgentDto?>() };
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task OnSearch()
    {
        await _table.ReloadServerData();
    }

    private async Task RefreshList()
    {
        await _table.ReloadServerData();
    }



    private async Task AddAgent()
    {
        var parameters = new DialogParameters();
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Medium, FullWidth = true };

        var dialog = await DialogService.ShowAsync<AiAgentDialog>("添加智能体", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await _table.ReloadServerData();
        }
    }

    private async Task EditAgent(AiAgentDto agent)
    {
        var parameters = new DialogParameters { ["Agent"] = agent };
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Medium, FullWidth = true };

        var dialog = await DialogService.ShowAsync<AiAgentDialog>("编辑智能体", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await _table.ReloadServerData();
        }
    }

    private async Task DeleteAgent(AiAgentDto agent)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除智能体 '{agent.Name}' 吗？此操作不可恢复。",
            yesText: "删除", cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                await AiAgentApi.DeleteAsync(agent.Id);
                Snackbar.Add("删除成功", Severity.Success);
                await _table.ReloadServerData();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task TestAgent(AiAgentDto agent)
    {
        var parameters = new DialogParameters { ["Agent"] = agent };
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Small, FullWidth = true };

        await DialogService.ShowAsync<AiAgentTestDialog>("测试智能体", parameters, options);
    }

    private async Task ToggleEnabledAsync(AiAgentDto agent)
    {
        try
        {
            await AiAgentApi.ToggleEnabledAsync(agent.Id, agent.IsEnabled);
            Snackbar.Add($"已{(agent.IsEnabled ? "启用" : "禁用")}智能体", Severity.Success);
        }
        catch (Exception ex)
        {
            // 恢复原状态
            agent.IsEnabled = !agent.IsEnabled;
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
            StateHasChanged();
        }
    }

    private Color GetProviderColor(AiProviderType providerType)
    {
        return providerType switch
        {
            AiProviderType.CoZe => Color.Primary,
            AiProviderType.MaxKB => Color.Secondary,
            AiProviderType.Dify => Color.Tertiary,
            AiProviderType.ChatGPT => Color.Success,
            _ => Color.Default
        };
    }

    // 机器人配置相关方法
    private async Task LoadRobotConfig()
    {
        try
        {
            _isLoadingConfig = true;

            // 直接使用SystemConfigManager加载配置
            _eYunRiskControlConfig = await SystemConfigManager.GetEYunRiskControlConfigAsync();

            // 🔧 注释冗余的配置加载完成日志 - 减少日志噪音
            // Logger.LogInformation("加载EYun风控配置完成 - EnableIncomingMessageSensitiveWordDetection: {Value}",
            //     _eYunRiskControlConfig.EnableIncomingMessageSensitiveWordDetection);

            // 验证配置完整性
            await ValidateConfigIntegrity();

            // 初始化文本字段
            InitializeTextFields();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载机器人配置失败: {ex.Message}", Severity.Error);
            Logger.LogError(ex, "加载机器人配置失败");

            // 加载失败时使用默认配置
            _eYunRiskControlConfig = ConfigDefaultValueFactory.CreateEYunRiskControlConfigDefault();
            InitializeTextFields();
        }
        finally
        {
            _isLoadingConfig = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// 强制从数据库重新加载配置，忽略缓存
    /// </summary>
    private async Task ForceReloadRobotConfig()
    {
        try
        {
            _isLoadingConfig = true;

            // 等待一小段时间确保数据库写入完成
            await Task.Delay(300);

            // 强制刷新配置缓存
            await SystemConfigManager.ForceRefreshAllConfigCacheAsync();
            Logger.LogInformation("已强制清理配置缓存");

            // 重新加载配置
            _eYunRiskControlConfig = await SystemConfigManager.GetEYunRiskControlConfigAsync();

            // 🔧 注释冗余的强制重新加载配置完成日志 - 减少日志噪音
            // Logger.LogInformation("强制重新加载EYun风控配置完成 - EnableIncomingMessageSensitiveWordDetection: {Value}",
            //     _eYunRiskControlConfig.EnableIncomingMessageSensitiveWordDetection);

            // 验证配置完整性
            await ValidateConfigIntegrity();

            // 初始化文本字段
            InitializeTextFields();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"强制重新加载机器人配置失败: {ex.Message}", Severity.Error);
            Logger.LogError(ex, "强制重新加载机器人配置失败");

            // 重新加载失败时使用默认配置
            _eYunRiskControlConfig = ConfigDefaultValueFactory.CreateEYunRiskControlConfigDefault();
            InitializeTextFields();
        }
        finally
        {
            _isLoadingConfig = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// 配置变更时的处理逻辑 - 简化版本，移除复杂的状态传递
    /// </summary>
    private async Task OnRobotConfigChanged()
    {
        // 更新敏感词列表
        UpdateSensitiveWordsFromText();
        UpdateIncomingMessageSensitiveWordsFromText();
        UpdateAiReplySensitiveWordsFromText();

        // 更新关键词列表
        UpdateAutoAcceptKeywordsFromText();

        // 触发UI更新
        StateHasChanged();
        await Task.Delay(50); // 确保UI更新完成
    }

    /// <summary>
    /// 初始化文本字段
    /// </summary>
    private void InitializeTextFields()
    {
        if (_eYunRiskControlConfig.SensitiveWords != null && _eYunRiskControlConfig.SensitiveWords.Any())
        {
            _sensitiveWordsText = string.Join(Environment.NewLine, _eYunRiskControlConfig.SensitiveWords);
        }

        if (_eYunRiskControlConfig.AutoAcceptKeywords != null && _eYunRiskControlConfig.AutoAcceptKeywords.Any())
        {
            _autoAcceptKeywordsText = string.Join(Environment.NewLine, _eYunRiskControlConfig.AutoAcceptKeywords);
        }

        if (_eYunRiskControlConfig.IncomingMessageSensitiveWords != null && _eYunRiskControlConfig.IncomingMessageSensitiveWords.Any())
        {
            _incomingMessageSensitiveWordsText = string.Join(Environment.NewLine, _eYunRiskControlConfig.IncomingMessageSensitiveWords);
        }

        if (_eYunRiskControlConfig.AIReplySensitiveWords != null && _eYunRiskControlConfig.AIReplySensitiveWords.Any())
        {
            _aiReplySensitiveWordsText = string.Join(Environment.NewLine, _eYunRiskControlConfig.AIReplySensitiveWords);
        }


    }

    /// <summary>
    /// 从文本更新敏感词列表
    /// </summary>
    private void UpdateSensitiveWordsFromText()
    {
        if (!string.IsNullOrEmpty(_sensitiveWordsText))
        {
            _eYunRiskControlConfig.SensitiveWords = _sensitiveWordsText
                .Split(new[] { Environment.NewLine, "\n" }, StringSplitOptions.RemoveEmptyEntries)
                .Where(w => !string.IsNullOrWhiteSpace(w))
                .Select(w => w.Trim())
                .ToList();
        }
    }

    /// <summary>
    /// 从文本更新自动接受关键词列表
    /// </summary>
    private void UpdateAutoAcceptKeywordsFromText()
    {
        if (!string.IsNullOrEmpty(_autoAcceptKeywordsText))
        {
            _eYunRiskControlConfig.AutoAcceptKeywords = _autoAcceptKeywordsText
                .Split(new[] { Environment.NewLine, "\n" }, StringSplitOptions.RemoveEmptyEntries)
                .Where(w => !string.IsNullOrWhiteSpace(w))
                .Select(w => w.Trim())
                .ToList();
        }
    }

    /// <summary>
    /// 从文本更新接收消息敏感词列表
    /// </summary>
    private void UpdateIncomingMessageSensitiveWordsFromText()
    {
        if (!string.IsNullOrEmpty(_incomingMessageSensitiveWordsText))
        {
            _eYunRiskControlConfig.IncomingMessageSensitiveWords = _incomingMessageSensitiveWordsText
                .Split(new[] { Environment.NewLine, "\n" }, StringSplitOptions.RemoveEmptyEntries)
                .Where(w => !string.IsNullOrWhiteSpace(w))
                .Select(w => w.Trim())
                .ToList();
        }
    }

    /// <summary>
    /// 从文本更新AI回复敏感词列表
    /// </summary>
    private void UpdateAiReplySensitiveWordsFromText()
    {
        if (!string.IsNullOrEmpty(_aiReplySensitiveWordsText))
        {
            _eYunRiskControlConfig.AIReplySensitiveWords = _aiReplySensitiveWordsText
                .Split(new[] { Environment.NewLine, "\n" }, StringSplitOptions.RemoveEmptyEntries)
                .Where(w => !string.IsNullOrWhiteSpace(w))
                .Select(w => w.Trim())
                .ToList();
        }
    }



    /// <summary>
    /// 验证配置完整性
    /// </summary>
    private async Task ValidateConfigIntegrity()
    {
        try
        {
            // 验证关键配置项
            var validationErrors = new List<string>();

            // 验证布尔值配置
            if (_eYunRiskControlConfig == null)
            {
                validationErrors.Add("配置对象为null");
            }
            else
            {
                // 🔧 注释冗余的配置验证详细日志 - 减少日志噪音，每次验证都会输出大量信息
                // Logger.LogInformation("配置验证 - EnableIncomingMessageSensitiveWordDetection: {Value}",
                //     _eYunRiskControlConfig.EnableIncomingMessageSensitiveWordDetection);
                // Logger.LogInformation("配置验证 - EnableAiReplyMessageSensitiveWordDetection: {Value}",
                //     _eYunRiskControlConfig.EnableAiReplyMessageSensitiveWordDetection);
                // Logger.LogInformation("配置验证 - EnableAutoAcceptFriends: {Value}",
                //     _eYunRiskControlConfig.EnableAutoAcceptFriends);

                // Logger.LogInformation("配置验证 - EnableKeywordMatching: {Value}",
                //     _eYunRiskControlConfig.EnableKeywordMatching);
                // Logger.LogInformation("配置验证 - UseAiForWelcome: {Value}",
                //     _eYunRiskControlConfig.UseAiForWelcome);
                // Logger.LogInformation("配置验证 - UseMaterialLibraryForWelcome: {Value}",
                //     _eYunRiskControlConfig.UseMaterialLibraryForWelcome);
            }

            if (validationErrors.Any())
            {
                Logger.LogError("配置验证失败: {Errors}", string.Join(", ", validationErrors));
                Snackbar.Add($"配置验证失败: {string.Join(", ", validationErrors)}", Severity.Warning);
            }
            else
            {
                // 🔧 注释冗余的配置验证通过日志 - 减少日志噪音
                // Logger.LogInformation("配置验证通过");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "配置验证异常");
        }
    }

    private async Task SaveRobotConfig()
    {
        try
        {
            _isSavingConfig = true;
            StateHasChanged();

            // 保存前更新文本字段到配置对象
            UpdateSensitiveWordsFromText();
            UpdateAutoAcceptKeywordsFromText();
            UpdateIncomingMessageSensitiveWordsFromText();
            UpdateAiReplySensitiveWordsFromText();


            // 保存前记录关键配置状态（简化版）
            var configSnapshot = new
            {
                EnableAutoAcceptFriends = _eYunRiskControlConfig.EnableAutoAcceptFriends
            };

            Logger.LogInformation("保存前关键配置状态 - EnableAutoAcceptFriends: {EnableAutoAcceptFriends}",
                configSnapshot.EnableAutoAcceptFriends);

            // 保存配置到数据库
            await SystemConfigManager.SetEYunRiskControlConfigAsync(_eYunRiskControlConfig);
            Logger.LogInformation("EYun风控配置已保存到数据库");

            // 使用统一配置变更通知机制
            await NotifyConfigurationChangedAsync("EYunRiskControl", "EYunRiskControlConfig", null, _eYunRiskControlConfig);

            // 等待系统同步
            await Task.Delay(500);

            Snackbar.Add("机器人配置保存成功", Severity.Success);

            // 强制从数据库重新加载配置以确保前端显示最新状态
            await ForceReloadRobotConfig();

            // 验证配置完整性和一致性
            await ValidateConfigSaveIntegrity(configSnapshot);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存机器人配置失败: {ex.Message}", Severity.Error);
            Logger.LogError(ex, "保存机器人配置失败");

            // 保存失败时重新加载配置，确保前端状态正确
            try
            {
                await ForceReloadRobotConfig();
            }
            catch (Exception reloadEx)
            {
                Logger.LogError(reloadEx, "保存失败后重新加载配置也失败");
            }
        }
        finally
        {
            _isSavingConfig = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// 验证配置保存完整性（简化版）
    /// </summary>
    private async Task ValidateConfigSaveIntegrity(object configSnapshot)
    {
        try
        {
            // 等待一小段时间确保配置已完全同步
            await Task.Delay(200);

            // 从数据库重新获取配置进行验证
            var savedConfig = await SystemConfigManager.GetEYunRiskControlConfigAsync();

            // 简单验证关键配置项是否保存成功
            var validationPassed = true;
            var validationMessages = new List<string>();

            // 验证关键开关配置

            if (savedConfig.EnableAutoAcceptFriends != _eYunRiskControlConfig.EnableAutoAcceptFriends)
            {
                validationPassed = false;
                validationMessages.Add("自动通过好友配置保存异常");
                Logger.LogWarning("配置验证失败 - EnableAutoAcceptFriends: 期望={Expected}, 实际={Actual}",
                    _eYunRiskControlConfig.EnableAutoAcceptFriends, savedConfig.EnableAutoAcceptFriends);
            }

            if (validationPassed)
            {
                Logger.LogInformation("配置保存验证成功：关键配置项已正确保存");
            }
            else
            {
                Logger.LogError("配置保存验证失败: {Messages}", string.Join(", ", validationMessages));
                Snackbar.Add($"配置保存验证失败: {string.Join(", ", validationMessages)}", Severity.Warning);

                // 如果验证失败，强制重新加载配置
                await ForceReloadRobotConfig();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "配置保存验证异常");
            Snackbar.Add("配置验证过程中发生异常，请检查配置是否正确保存", Severity.Warning);
        }
    }

    /// <summary>
    /// 实现基类抽象方法：同步配置
    /// </summary>
    protected override async Task OnSyncConfigAsync()
    {
        try
        {
            Logger.LogInformation("开始同步AI管理配置");

            // 刷新智能体列表
            await LoadAgents();

            // 刷新机器人配置
            await LoadRobotConfig();

            Logger.LogInformation("AI管理配置同步完成");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "AI管理配置同步失败");
            throw;
        }
    }
}