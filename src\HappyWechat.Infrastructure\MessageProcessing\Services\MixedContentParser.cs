using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using HappyWechat.Application.DTOs.Wrappers.EYun;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 混合内容类型
/// </summary>
public enum MixedContentType
{
    Text,
    Image,
    Voice,
    File,
    Url,
    AtMention
}

/// <summary>
/// 混合内容项
/// </summary>
public class MixedContentItem
{
    public MixedContentType Type { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Url { get; set; }
    public string? FileName { get; set; }
    public string? FileType { get; set; }
    public int Order { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 解析结果
/// </summary>
public class MixedContentParseResult
{
    public List<MixedContentItem> Items { get; set; } = new();
    public List<string> ExtractedUrls { get; set; } = new();
    public List<string> AtMentions { get; set; } = new();
    public bool HasMedia { get; set; }
    public string ProcessedText { get; set; } = string.Empty;
}

/// <summary>
/// 图文混排解析服务接口
/// </summary>
public interface IMixedContentParser
{
    /// <summary>
    /// 解析混合内容消息
    /// </summary>
    Task<MixedContentParseResult> ParseAsync(WxCallbackMessageDto callbackMessage);
    
    /// <summary>
    /// 提取URL并分类
    /// </summary>
    Task<List<string>> ExtractUrlsAsync(string content);
    
    /// <summary>
    /// 分割消息为有序项目
    /// </summary>
    Task<List<MixedContentItem>> SplitMessageAsync(MixedContentParseResult parseResult);
}

/// <summary>
/// 图文混排解析服务实现
/// </summary>
public class MixedContentParser : IMixedContentParser
{
    private readonly ILogger<MixedContentParser> _logger;
    
    // URL正则表达式
    private static readonly Regex UrlRegex = new(
        @"https?://[^\s<>""]+|www\.[^\s<>""]+",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);
    
    // @提及正则表达式
    private static readonly Regex AtMentionRegex = new(
        @"@([^\s@]+)",
        RegexOptions.Compiled);

    public MixedContentParser(ILogger<MixedContentParser> logger)
    {
        _logger = logger;
    }

    public async Task<MixedContentParseResult> ParseAsync(WxCallbackMessageDto callbackMessage)
    {
        try
        {
            _logger.LogDebug("开始解析混合内容 - MessageType: {MessageType}, WcId: {WcId}", 
                callbackMessage.MessageType, callbackMessage.WcId);

            var result = new MixedContentParseResult();
            var content = callbackMessage.Data?.Content ?? "";
            
            // 根据消息类型进行解析
            switch (callbackMessage.MessageType)
            {
                case "60001": // 私聊文本
                case "80001": // 群聊文本
                    await ParseTextMessageAsync(content, result, callbackMessage);
                    break;
                    
                case "60002": // 私聊图片
                case "80002": // 群聊图片
                    await ParseImageMessageAsync(content, result, callbackMessage);
                    break;
                    
                case "60004": // 私聊语音
                case "80004": // 群聊语音
                    await ParseVoiceMessageAsync(content, result, callbackMessage);
                    break;
                    
                // 🔧 重构：使用60009/80009替代60008/80008处理文件发送完成消息
                case "60009": // 私聊文件发送完成
                case "80009": // 群聊文件发送完成
                    await ParseFileMessageAsync(content, result, callbackMessage);
                    break;
                    
                default:
                    _logger.LogWarning("未知的消息类型 - MessageType: {MessageType}", callbackMessage.MessageType);
                    break;
            }

            // 提取@提及
            result.AtMentions = ExtractAtMentions(content);
            
            // 设置处理后的文本
            result.ProcessedText = content;

            _logger.LogDebug("混合内容解析完成 - Items: {ItemCount}, URLs: {UrlCount}, AtMentions: {AtCount}, HasMedia: {HasMedia}", 
                result.Items.Count, result.ExtractedUrls.Count, result.AtMentions.Count, result.HasMedia);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析混合内容失败 - MessageType: {MessageType}", callbackMessage.MessageType);
            return new MixedContentParseResult();
        }
    }

    public async Task<List<string>> ExtractUrlsAsync(string content)
    {
        try
        {
            var matches = UrlRegex.Matches(content);
            var urls = matches.Cast<Match>().Select(m => m.Value).Distinct().ToList();
            
            _logger.LogDebug("提取URL完成 - Count: {Count}, URLs: {URLs}", 
                urls.Count, string.Join(", ", urls));
            
            return await Task.FromResult(urls);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取URL失败");
            return new List<string>();
        }
    }

    public async Task<List<MixedContentItem>> SplitMessageAsync(MixedContentParseResult parseResult)
    {
        try
        {
            // 按顺序标记排序
            var sortedItems = parseResult.Items.OrderBy(item => item.Order).ToList();
            
            _logger.LogDebug("消息分割完成 - Items: {Count}", sortedItems.Count);
            
            return await Task.FromResult(sortedItems);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息分割失败");
            return new List<MixedContentItem>();
        }
    }

    private async Task ParseTextMessageAsync(string content, MixedContentParseResult result, WxCallbackMessageDto callbackMessage)
    {
        // 提取URL
        result.ExtractedUrls = await ExtractUrlsAsync(content);
        
        // 创建文本内容项
        result.Items.Add(new MixedContentItem
        {
            Type = MixedContentType.Text,
            Content = content,
            Order = 1,
            Metadata = 
            {
                ["MessageType"] = callbackMessage.MessageType,
                ["FromUser"] = callbackMessage.Data?.FromUser ?? "",
                ["Timestamp"] = callbackMessage.Data?.Timestamp ?? 0
            }
        });

        // 如果有URL，添加URL项
        for (int i = 0; i < result.ExtractedUrls.Count; i++)
        {
            result.Items.Add(new MixedContentItem
            {
                Type = MixedContentType.Url,
                Content = result.ExtractedUrls[i],
                Url = result.ExtractedUrls[i],
                Order = 2 + i,
                Metadata = { ["UrlIndex"] = i }
            });
        }
    }

    private async Task ParseImageMessageAsync(string content, MixedContentParseResult result, WxCallbackMessageDto callbackMessage)
    {
        result.HasMedia = true;
        
        result.Items.Add(new MixedContentItem
        {
            Type = MixedContentType.Image,
            Content = content,
            Order = 1,
            Metadata = 
            {
                ["MessageType"] = callbackMessage.MessageType,
                ["FromUser"] = callbackMessage.Data?.FromUser ?? "",
                ["Timestamp"] = callbackMessage.Data?.Timestamp ?? 0,
                ["RequiresDownload"] = true
            }
        });

        await Task.CompletedTask;
    }

    private async Task ParseVoiceMessageAsync(string content, MixedContentParseResult result, WxCallbackMessageDto callbackMessage)
    {
        result.HasMedia = true;
        
        result.Items.Add(new MixedContentItem
        {
            Type = MixedContentType.Voice,
            Content = content,
            Order = 1,
            Metadata = 
            {
                ["MessageType"] = callbackMessage.MessageType,
                ["FromUser"] = callbackMessage.Data?.FromUser ?? "",
                ["Timestamp"] = callbackMessage.Data?.Timestamp ?? 0,
                ["RequiresDownload"] = true,
                ["RequiresConversion"] = true, // silk转MP3
                ["OriginalFormat"] = "silk",
                ["TargetFormat"] = "mp3"
            }
        });

        await Task.CompletedTask;
    }

    private async Task ParseFileMessageAsync(string content, MixedContentParseResult result, WxCallbackMessageDto callbackMessage)
    {
        result.HasMedia = true;
        
        // 尝试从content中提取文件信息
        var fileName = ExtractFileNameFromContent(content);
        var fileType = ExtractFileTypeFromFileName(fileName);
        
        result.Items.Add(new MixedContentItem
        {
            Type = MixedContentType.File,
            Content = content,
            FileName = fileName,
            FileType = fileType,
            Order = 1,
            Metadata = 
            {
                ["MessageType"] = callbackMessage.MessageType,
                ["FromUser"] = callbackMessage.Data?.FromUser ?? "",
                ["Timestamp"] = callbackMessage.Data?.Timestamp ?? 0,
                ["RequiresDownload"] = true
            }
        });

        await Task.CompletedTask;
    }

    private List<string> ExtractAtMentions(string content)
    {
        try
        {
            var matches = AtMentionRegex.Matches(content);
            return matches.Cast<Match>().Select(m => m.Groups[1].Value).Distinct().ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取@提及失败");
            return new List<string>();
        }
    }

    private string ExtractFileNameFromContent(string content)
    {
        // 简单的文件名提取逻辑，可能需要根据实际content格式调整
        try
        {
            // 如果content是XML格式，可能需要解析XML
            // 这里先返回一个默认值
            return $"file_{DateTime.Now:yyyyMMddHHmmss}";
        }
        catch
        {
            return "unknown_file";
        }
    }

    private string ExtractFileTypeFromFileName(string fileName)
    {
        try
        {
            var extension = Path.GetExtension(fileName)?.ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "image",
                ".mp3" or ".wav" or ".m4a" or ".aac" => "audio",
                ".mp4" or ".avi" or ".mov" or ".wmv" => "video",
                ".pdf" or ".doc" or ".docx" or ".txt" => "document",
                _ => "file"
            };
        }
        catch
        {
            return "file";
        }
    }
}
