using System.Diagnostics;
using HappyWechat.Application.DTOs.AiProvider;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.ValueObjects;
using HappyWechat.Domain.ValueObjects.Enums;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.AiProvider;

/// <summary>
/// AI提供商基类
/// </summary>
public abstract class BaseAiProvider : IAiProvider
{
    protected readonly ILogger Logger;
    protected readonly IHttpClientFactory HttpClientFactory;
    protected readonly IAiMessageTemplateService? TemplateService;

    protected BaseAiProvider(ILogger logger, IHttpClientFactory httpClientFactory, IAiMessageTemplateService? templateService = null)
    {
        Logger = logger;
        HttpClientFactory = httpClientFactory;
        TemplateService = templateService;
    }

    /// <summary>
    /// 提供商类型
    /// </summary>
    public abstract string ProviderType { get; }

    /// <summary>
    /// 发送消息并获取AI响应
    /// </summary>
    public async Task<AiResponse> SendMessageAsync(AiRequest request, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 🔧 精简日志 - 注释掉AI请求开始处理日志，减少日志噪音
            // Logger.LogInformation($"[{ProviderType}] 开始处理AI请求，用户ID: {request.UserId}");

            var result = await SendMessageInternalAsync(request, cancellationToken);

            stopwatch.Stop();
            result.ResponseTimeMs = stopwatch.ElapsedMilliseconds;

            // 🔧 精简日志 - 注释掉AI请求处理完成日志，减少日志噪音
            // Logger.LogInformation($"[{ProviderType}] AI请求处理完成，耗时: {result.ResponseTimeMs}ms，成功: {result.IsSuccess}");

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            Logger.LogError(ex, $"[{ProviderType}] AI请求处理失败，用户ID: {request.UserId}");

            var failureResponse = AiResponse.Failure($"AI请求处理失败: {ex.Message}");
            failureResponse.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
            return failureResponse;
        }
    }

    /// <summary>
    /// 发送消息并获取AI响应（支持消息上下文）
    /// </summary>
    public async Task<AiResponse> SendMessageAsync(AiRequest request, AiMessageContext? messageContext, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 🔧 精简日志 - 注释掉AI请求开始处理日志，减少日志噪音
            // Logger.LogInformation($"[{ProviderType}] 开始处理AI请求，用户ID: {request.UserId}");

            // 如果有消息上下文且支持模板，则进行模板化处理
            if (messageContext != null && TemplateService != null)
            {
                var providerType = GetProviderType();

                // 设置提供商类型
                messageContext.ProviderType = providerType.ToString();

                var templatedMessage = await TemplateService.BuildMessageTemplateAsync(messageContext, providerType);

                // 创建新的请求对象，使用模板化后的消息
                var templatedRequest = new AiRequest
                {
                    Message = templatedMessage,
                    UserId = request.UserId,
                    ConversationId = request.ConversationId,
                    SystemPrompt = request.SystemPrompt,
                    Temperature = request.Temperature,
                    AdditionalParameters = request.AdditionalParameters,
                    Files = request.Files
                };

                var result = await SendMessageInternalAsync(templatedRequest, cancellationToken);

                stopwatch.Stop();
                result.ResponseTimeMs = stopwatch.ElapsedMilliseconds;

                // 设置处理结束时间
                messageContext.ProcessEndTime = DateTime.UtcNow;

                // 🔧 精简日志 - 注释掉AI请求处理完成日志，减少日志噪音
                // Logger.LogInformation($"[{ProviderType}] AI请求处理完成（模板化），耗时: {result.ResponseTimeMs}ms，成功: {result.IsSuccess}");

                return result;
            }
            else
            {
                // 使用原有逻辑
                var result = await SendMessageInternalAsync(request, cancellationToken);

                stopwatch.Stop();
                result.ResponseTimeMs = stopwatch.ElapsedMilliseconds;

                // 🔧 精简日志 - 注释掉AI请求处理完成日志，减少日志噪音
                // Logger.LogInformation($"[{ProviderType}] AI请求处理完成，耗时: {result.ResponseTimeMs}ms，成功: {result.IsSuccess}");

                return result;
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            // 🔧 增强异常处理：根据异常类型生成不同的错误消息
            string errorMessage;
            if (ex is TimeoutException || ex is TaskCanceledException || ex is OperationCanceledException)
            {
                errorMessage = $"{ProviderType}请求超时: {ex.Message}";
                Logger.LogWarning(ex, $"[{ProviderType}] AI请求超时，用户ID: {request.UserId}");
            }
            else if (ex is HttpRequestException)
            {
                errorMessage = $"{ProviderType}网络异常: {ex.Message}";
                Logger.LogWarning(ex, $"[{ProviderType}] AI请求网络异常，用户ID: {request.UserId}");
            }
            else if (ex is ArgumentException || ex is InvalidOperationException)
            {
                errorMessage = $"{ProviderType}配置错误: {ex.Message}";
                Logger.LogError(ex, $"[{ProviderType}] AI请求配置错误，用户ID: {request.UserId}");
            }
            else
            {
                errorMessage = $"{ProviderType}请求异常: {ex.Message}";
                Logger.LogError(ex, $"[{ProviderType}] AI请求处理失败，用户ID: {request.UserId}");
            }

            var failureResponse = AiResponse.Failure(errorMessage);
            failureResponse.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
            return failureResponse;
        }
    }

    /// <summary>
    /// 具体的消息发送实现（由子类实现）
    /// </summary>
    protected abstract Task<AiResponse> SendMessageInternalAsync(AiRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// 获取提供商类型枚举（由子类实现）
    /// </summary>
    protected virtual AiProviderType GetProviderType()
    {
        return ProviderType switch
        {
            "CoZe" => AiProviderType.CoZe,
            "Dify" => AiProviderType.Dify,
            "MaxKB" => AiProviderType.MaxKB,
            "ChatGPT" => AiProviderType.ChatGPT,
            _ => AiProviderType.ChatGPT // 默认值
        };
    }

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    public abstract Task<bool> ValidateConfigAsync(object config);

    /// <summary>
    /// 获取健康状态
    /// </summary>
    public virtual async Task<bool> IsHealthyAsync()
    {
        try
        {
            // 默认实现：尝试发送一个简单的测试请求
            var testRequest = new AiRequest
            {
                Message = "Hello",
                UserId = "health_check"
            };
            
            var response = await SendMessageAsync(testRequest);
            return response.IsSuccess;
        }
        catch
        {
            return false;
        }
    }
}