using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.AI;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.MessageQueue;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.AiConfig;
using HappyWechat.Infrastructure.MessageProcessing.Services;
using HappyWechat.Infrastructure.MessageQueue.Simplified;

namespace HappyWechat.Infrastructure.Services;

/// <summary>
/// 简化AI服务实现 - 集成真实AI Agent调用
/// </summary>
public class SimplifiedAiService : IAiService
{
    private readonly ILogger<SimplifiedAiService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public SimplifiedAiService(
        ILogger<SimplifiedAiService> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task<AiMessageDto?> ProcessMessageAsync(AiMessageDto message, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🤖 处理AI消息 - Content: {Content}",
                message.Content?.Substring(0, Math.Min(50, message.Content?.Length ?? 0)));

            // 获取AI Agent配置
            var aiAgentId = await GetAiAgentIdForMessageAsync(message);
            if (aiAgentId == null)
            {
                _logger.LogWarning("未找到AI Agent配置 - WxManagerId: {WxManagerId}, IsGroup: {IsGroup}",
                    message.WxManagerId, message.IsGroupMessage);
                return null;
            }

            // 调用真实AI处理
            var aiAgentService = _serviceProvider.GetRequiredService<IAiAgentService>();
            var aiResponse = await aiAgentService.ProcessMessageAsync(aiAgentId.Value, message.Content ?? "", message);

            if (string.IsNullOrWhiteSpace(aiResponse))
            {
                _logger.LogWarning("AI返回空响应 - AgentId: {AgentId}", aiAgentId);
                return null;
            }

            // 获取WcId
            var wcId = await GetWcIdFromWxManagerIdAsync(message.WxManagerId);
            if (string.IsNullOrEmpty(wcId))
            {
                _logger.LogWarning("无法获取WcId - WxManagerId: {WxManagerId}", message.WxManagerId);
                return null;
            }

            // 使用AiResponseProcessor处理AI响应
            var responseProcessor = _serviceProvider.GetRequiredService<IAiResponseProcessor>();
            var processedResult = await responseProcessor.ProcessAiResponseAsync(
                aiResponse,
                message.WxManagerId,
                wcId,
                message.FromUser ?? "",
                null, // options
                null, // userId
                null, // atUsers
                message.GroupId, // fromGroup
                message.FromUser, // fromGroupUser
                null); // fromGroupUserNickName

            // 将处理结果路由到发送队列而非直接发送
            await RouteToSendQueueAsync(processedResult, message, wcId);

            return new AiMessageDto
            {
                WxManagerId = message.WxManagerId,
                Content = "AI处理完成，已加入发送队列",
                MessageType = "text",
                FromUser = "AI",
                ToUser = message.FromUser,
                IsGroupMessage = message.IsGroupMessage,
                GroupId = message.GroupId,
                CreatedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ AI消息处理失败");
            return null;
        }
    }

    public async Task<string?> GenerateReplyAsync(string content, string context, CancellationToken cancellationToken = default)
    {
        try
        {
            // 这个方法保持简单实现，主要逻辑在ProcessMessageAsync中
            _logger.LogDebug("生成AI回复 - Content: {Content}", content.Substring(0, Math.Min(50, content.Length)));
            return $"AI回复: {content}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ AI回复生成失败");
            return null;
        }
    }

    /// <summary>
    /// 根据WxManagerId获取WcId
    /// </summary>
    private async Task<string?> GetWcIdFromWxManagerIdAsync(Guid wxManagerId)
    {
        try
        {
            var wxManagerRepository = _serviceProvider.GetRequiredService<IWxManagerRepository>();
            var manager = await wxManagerRepository.GetByIdAsync(wxManagerId);
            return manager?.WcId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取WcId失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
    }

    /// <summary>
    /// 根据消息获取AI Agent ID
    /// </summary>
    private async Task<Guid?> GetAiAgentIdForMessageAsync(AiMessageDto message)
    {
        try
        {
            var aiConfigService = _serviceProvider.GetRequiredService<SimplifiedAiConfigService>();

            if (message.IsGroupMessage && !string.IsNullOrEmpty(message.GroupId))
            {
                // 群聊消息：获取群组AI配置
                var groupConfig = await aiConfigService.GetGroupConfigAsync(message.GroupId, message.WxManagerId);
                if (groupConfig?.IsEnabled == true && groupConfig.AiAgentId.HasValue)
                {
                    return groupConfig.AiAgentId.Value;
                }
            }
            else if (!string.IsNullOrEmpty(message.FromUser))
            {
                // 私聊消息：获取联系人AI配置
                var contactConfig = await aiConfigService.GetContactConfigAsync(message.FromUser, message.WxManagerId);
                if (contactConfig?.IsEnabled == true && contactConfig.AiAgentId.HasValue)
                {
                    return contactConfig.AiAgentId.Value;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI Agent ID失败");
            return null;
        }
    }

    /// <summary>
    /// 将处理结果路由到发送队列
    /// </summary>
    private async Task RouteToSendQueueAsync(AiResponseProcessingResult processedResult, AiMessageDto message, string wcId)
    {
        try
        {
            var queueService = _serviceProvider.GetRequiredService<ISimplifiedQueueService>();
            var mixedMessage = processedResult.MixedMessage;

            if (mixedMessage?.MessageItems == null || !mixedMessage.MessageItems.Any())
            {
                _logger.LogWarning("AI响应处理结果为空，无消息需要发送");
                return;
            }

            _logger.LogInformation("开始路由AI响应到发送队列 - Messages: {Count}, WxManagerId: {WxManagerId}",
                mixedMessage.MessageItems.Count, message.WxManagerId);

            foreach (var messageItem in mixedMessage.MessageItems)
            {
                await RouteMessageItemToSendQueueAsync(queueService, messageItem, message, wcId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "路由到发送队列失败");
        }
    }

    /// <summary>
    /// 路由单个消息到对应的发送队列
    /// </summary>
    private async Task RouteMessageItemToSendQueueAsync(ISimplifiedQueueService queueService, MessageQueueItem messageItem, AiMessageDto originalMessage, string wcId)
    {
        try
        {
            switch (messageItem.MessageType)
            {
                case MessageContentType.Text:
                    await RouteTextMessageAsync(queueService, messageItem, originalMessage, wcId);
                    break;
                case MessageContentType.Image:
                    await RouteImageMessageAsync(queueService, messageItem, originalMessage, wcId);
                    break;
                case MessageContentType.File:
                    await RouteFileMessageAsync(queueService, messageItem, originalMessage, wcId);
                    break;
                case MessageContentType.Voice:
                    await RouteVoiceMessageAsync(queueService, messageItem, originalMessage, wcId);
                    break;
                default:
                    _logger.LogWarning("未知消息类型，跳过发送 - Type: {Type}", messageItem.MessageType);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "路由消息失败 - Type: {Type}", messageItem.Type);
        }
    }

    /// <summary>
    /// 路由文本消息到发送队列
    /// </summary>
    private async Task RouteTextMessageAsync(ISimplifiedQueueService queueService, MessageQueueItem messageItem, AiMessageDto originalMessage, string wcId)
    {
        var command = new WxSendTextMessageCommand
        {
            WId = wcId,
            WcId = originalMessage.IsGroupMessage ? originalMessage.GroupId! : originalMessage.FromUser!,
            Content = messageItem.Content,
            At = null, // MessageQueueItem没有AtUsers属性，暂时设为null
            FromGroup = originalMessage.IsGroupMessage ? originalMessage.GroupId : null,
            FromGroupUser = originalMessage.FromUser,
            FromGroupUserNickName = null
        };

        var messageId = await queueService.EnqueueAsync(originalMessage.WxManagerId, "send_text", command, CancellationToken.None);
        _logger.LogDebug("文本消息已入队 - MessageId: {MessageId}, Content: {Content}",
            messageId, messageItem.Content?.Substring(0, Math.Min(50, messageItem.Content?.Length ?? 0)));
    }

    /// <summary>
    /// 路由图片消息到发送队列
    /// </summary>
    private async Task RouteImageMessageAsync(ISimplifiedQueueService queueService, MessageQueueItem messageItem, AiMessageDto originalMessage, string wcId)
    {
        var command = new WxSendImageMessageCommand
        {
            WId = wcId,
            WcId = originalMessage.IsGroupMessage ? originalMessage.GroupId! : originalMessage.FromUser!,
            ImageUrl = messageItem.FileUrl,
            ImagePath = messageItem.FilePath,
            Content = messageItem.FileUrl ?? messageItem.FilePath ?? ""
        };

        var messageId = await queueService.EnqueueAsync(originalMessage.WxManagerId, "send_image", command, CancellationToken.None);
        _logger.LogDebug("图片消息已入队 - MessageId: {MessageId}, ImageUrl: {ImageUrl}",
            messageId, messageItem.FileUrl);
    }

    /// <summary>
    /// 路由文件消息到发送队列
    /// </summary>
    private async Task RouteFileMessageAsync(ISimplifiedQueueService queueService, MessageQueueItem messageItem, AiMessageDto originalMessage, string wcId)
    {
        var command = new WxSendFileMessageCommand
        {
            WId = wcId,
            WcId = originalMessage.IsGroupMessage ? originalMessage.GroupId! : originalMessage.FromUser!,
            FilePath = messageItem.FilePath ?? messageItem.FileUrl ?? "",
            FileName = Path.GetFileName(messageItem.FilePath ?? messageItem.FileUrl ?? "unknown_file")
        };

        var messageId = await queueService.EnqueueAsync(originalMessage.WxManagerId, "send_file", command, CancellationToken.None);
        _logger.LogDebug("文件消息已入队 - MessageId: {MessageId}, FileName: {FileName}",
            messageId, command.FileName);
    }

    /// <summary>
    /// 路由语音消息到发送队列
    /// </summary>
    private async Task RouteVoiceMessageAsync(ISimplifiedQueueService queueService, MessageQueueItem messageItem, AiMessageDto originalMessage, string wcId)
    {
        var command = new WxSendVoiceMessageCommand
        {
            WId = wcId,
            WcId = originalMessage.IsGroupMessage ? originalMessage.GroupId! : originalMessage.FromUser!,
            VoiceFilePath = messageItem.FilePath ?? messageItem.FileUrl ?? "",
            Content = messageItem.FileUrl ?? messageItem.FilePath ?? "",
            Length = 0 // 默认时长
        };

        var messageId = await queueService.EnqueueAsync(originalMessage.WxManagerId, "send_voice", command, CancellationToken.None);
        _logger.LogDebug("语音消息已入队 - MessageId: {MessageId}, VoiceFilePath: {VoiceFilePath}",
            messageId, command.VoiceFilePath);
    }
}