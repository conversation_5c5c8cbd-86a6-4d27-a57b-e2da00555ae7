using HappyWechat.Application.DTOs.AiProvider;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.ValueObjects;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Services;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// AI消息模板服务实现
/// </summary>
public class AiMessageTemplateService : IAiMessageTemplateService
{
    private readonly IContactNicknameService _contactNicknameService;
    private readonly ILogger<AiMessageTemplateService> _logger;

    public AiMessageTemplateService(
        IContactNicknameService contactNicknameService,
        ILogger<AiMessageTemplateService> logger)
    {
        _contactNicknameService = contactNicknameService;
        _logger = logger;
    }

    public async Task<string> BuildMessageTemplateAsync(AiMessageContext context, AiProviderType providerType)
    {
        var useTemplate = ShouldUseTemplate(providerType);
        
        if (context.IsMediaMessage)
        {
            return await BuildMediaMessageTemplateAsync(context, useTemplate);
        }
        else
        {
            return await BuildTextMessageTemplateAsync(context, useTemplate);
        }
    }

    public async Task<string> BuildTextMessageTemplateAsync(AiMessageContext context, bool useTemplate = true)
    {
        if (!useTemplate)
        {
            return context.Content;
        }

        try
        {
            // 确保昵称信息已获取
            await EnsureNicknamesAsync(context);

            if (context.IsGroupMessage)
            {
                return AiMessageTemplate.GROUP_TEXT_TEMPLATE
                    .Replace("{WxManagerId}", context.WxManagerId.ToString())
                    .Replace("{WId}", context.WId)
                    .Replace("{WcId}", context.WcId)
                    .Replace("{MessageTypeDescription}", MediaTypeDescriptions.GetDescription(context.MessageType))
                    .Replace("{Timestamp}", context.Timestamp.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture))
                    .Replace("{FromUser}", context.FromUser)
                    .Replace("{FromUserNickName}", context.FromUserNickName ?? "未知用户")
                    .Replace("{FromGroup}", context.FromGroup ?? string.Empty)
                    .Replace("{FromGroupUser}", context.FromGroupUser ?? context.FromUser)
                    .Replace("{FromGroupUserNickName}", context.FromGroupUserNickName ?? "未知用户")
                    .Replace("{AtList}", string.Join(", ", context.AtList))
                    .Replace("{Content}", context.Content)
                    .Replace("{ProviderType}", context.ProviderType ?? "未知")
                    .Replace("{ProcessTime}", CalculateProcessTime(context));
            }
            else
            {
                return AiMessageTemplate.PRIVATE_TEXT_TEMPLATE
                    .Replace("{WxManagerId}", context.WxManagerId.ToString())
                    .Replace("{WId}", context.WId)
                    .Replace("{WcId}", context.WcId)
                    .Replace("{MessageTypeDescription}", MediaTypeDescriptions.GetDescription(context.MessageType))
                    .Replace("{Timestamp}", context.Timestamp.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture))
                    .Replace("{FromUser}", context.FromUser)
                    .Replace("{FromUserNickName}", context.FromUserNickName ?? "未知用户")
                    .Replace("{Content}", context.Content)
                    .Replace("{ProviderType}", context.ProviderType ?? "未知")
                    .Replace("{ProcessTime}", CalculateProcessTime(context));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建文本消息模板失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}", 
                context.WxManagerId, context.FromUser);
            
            // 失败时返回原始内容
            return context.Content;
        }
    }

    public async Task<string> BuildMediaMessageTemplateAsync(AiMessageContext context, bool useTemplate = true)
    {
        if (!useTemplate)
        {
            // 对于不使用模板的提供商，返回原有的媒体格式
            return BuildLegacyMediaContent(context);
        }

        try
        {
            // 确保昵称信息已获取
            await EnsureNicknamesAsync(context);

            // 构建媒体内容描述
            var mediaContent = BuildMediaContentDescription(context);
            var mediaType = MediaTypeDescriptions.GetDescription(context.MessageType);

            if (context.IsGroupMessage)
            {
                return AiMessageTemplate.GROUP_MEDIA_TEMPLATE
                    .Replace("{WxManagerId}", context.WxManagerId.ToString())
                    .Replace("{WId}", context.WId)
                    .Replace("{WcId}", context.WcId)
                    .Replace("{MessageTypeDescription}", MediaTypeDescriptions.GetDescription(context.MessageType))
                    .Replace("{Timestamp}", context.Timestamp.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture))
                    .Replace("{FromUser}", context.FromUser)
                    .Replace("{FromUserNickName}", context.FromUserNickName ?? "未知用户")
                    .Replace("{FromGroup}", context.FromGroup ?? string.Empty)
                    .Replace("{FromGroupUser}", context.FromGroupUser ?? context.FromUser)
                    .Replace("{FromGroupUserNickName}", context.FromGroupUserNickName ?? "未知用户")
                    .Replace("{AtList}", string.Join(", ", context.AtList))
                    .Replace("{MediaContent}", mediaContent)
                    .Replace("{MediaType}", mediaType)
                    .Replace("{ProviderType}", context.ProviderType ?? "未知")
                    .Replace("{ProcessTime}", CalculateProcessTime(context));
            }
            else
            {
                return AiMessageTemplate.PRIVATE_MEDIA_TEMPLATE
                    .Replace("{WxManagerId}", context.WxManagerId.ToString())
                    .Replace("{WId}", context.WId)
                    .Replace("{WcId}", context.WcId)
                    .Replace("{MessageTypeDescription}", MediaTypeDescriptions.GetDescription(context.MessageType))
                    .Replace("{Timestamp}", context.Timestamp.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture))
                    .Replace("{FromUser}", context.FromUser)
                    .Replace("{FromUserNickName}", context.FromUserNickName ?? "未知用户")
                    .Replace("{MediaContent}", mediaContent)
                    .Replace("{MediaType}", mediaType)
                    .Replace("{ProviderType}", context.ProviderType ?? "未知")
                    .Replace("{ProcessTime}", CalculateProcessTime(context));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建媒体消息模板失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}", 
                context.WxManagerId, context.FromUser);
            
            // 失败时返回原有格式
            return BuildLegacyMediaContent(context);
        }
    }

    public bool ShouldUseTemplate(AiProviderType providerType)
    {
        return providerType switch
        {
            AiProviderType.CoZe => true,
            AiProviderType.Dify => true,
            AiProviderType.MaxKB => true,
            AiProviderType.ChatGPT => false,
            _ => false
        };
    }

    public async Task<AiMessageContext> BuildMessageContextAsync(
        WxCallbackMessageDto callbackMessage,
        Guid wxManagerId,
        List<string>? mediaDescriptions = null,
        List<string>? mediaUrls = null)
    {
        var context = new AiMessageContext
        {
            WxManagerId = wxManagerId,
            WId = callbackMessage.Data?.WId ?? callbackMessage.WcId ?? string.Empty,
            WcId = callbackMessage.WcId,
            FromUser = callbackMessage.Data?.FromUser ?? string.Empty,
            MessageType = callbackMessage.MessageType,
            Content = callbackMessage.Data?.Content ?? string.Empty,
            IsGroupMessage = !string.IsNullOrEmpty(callbackMessage.Data?.FromGroup),
            FromGroup = callbackMessage.Data?.FromGroup,
            FromGroupUser = callbackMessage.Data?.FromUser,
            MediaDescriptions = mediaDescriptions ?? new List<string>(),
            MediaUrls = mediaUrls ?? new List<string>(),
            IsMediaMessage = mediaDescriptions?.Any() == true || mediaUrls?.Any() == true,
            OriginalCallbackMessage = callbackMessage,
            Timestamp = DateTime.UtcNow
        };

        return context;
    }

    /// <summary>
    /// 确保昵称信息已获取
    /// </summary>
    private async Task EnsureNicknamesAsync(AiMessageContext context)
    {
        try
        {
            if (context.IsGroupMessage)
            {
                // 群消息：获取群成员昵称
                if (string.IsNullOrEmpty(context.FromGroupUserNickName) && 
                    !string.IsNullOrEmpty(context.FromGroup) && 
                    !string.IsNullOrEmpty(context.FromGroupUser))
                {
                    context.FromGroupUserNickName = await _contactNicknameService.GetGroupMemberNicknameAsync(
                        context.WxManagerId, context.FromGroup, context.FromGroupUser);
                }
            }
            else
            {
                // 私聊消息：获取联系人昵称
                if (string.IsNullOrEmpty(context.FromUserNickName) && !string.IsNullOrEmpty(context.FromUser))
                {
                    context.FromUserNickName = await _contactNicknameService.GetContactNicknameAsync(
                        context.WxManagerId, context.FromUser);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取用户昵称失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}, IsGroup: {IsGroup}", 
                context.WxManagerId, context.FromUser, context.IsGroupMessage);
        }
    }

    /// <summary>
    /// 构建媒体内容描述 - 包含外链URL
    /// </summary>
    private string BuildMediaContentDescription(AiMessageContext context)
    {
        if (!context.MediaDescriptions.Any() && !context.MediaUrls.Any())
        {
            return "无媒体内容";
        }

        var contentBuilder = new System.Text.StringBuilder();

        // 优先使用媒体描述
        if (context.MediaDescriptions.Any())
        {
            for (int i = 0; i < context.MediaDescriptions.Count; i++)
            {
                contentBuilder.AppendLine($"{i + 1}. {context.MediaDescriptions[i]}");

                // 如果有对应的URL，也包含进去
                if (i < context.MediaUrls.Count && !string.IsNullOrEmpty(context.MediaUrls[i]))
                {
                    contentBuilder.AppendLine($"   URL: {context.MediaUrls[i]}");
                }
            }
        }
        // 如果没有描述但有URL，直接使用URL
        else if (context.MediaUrls.Any())
        {
            for (int i = 0; i < context.MediaUrls.Count; i++)
            {
                var mediaType = MediaTypeDescriptions.GetDescription(context.MessageType);
                contentBuilder.AppendLine($"{i + 1}. {mediaType}: {context.MediaUrls[i]}");
            }
        }

        return contentBuilder.ToString().TrimEnd();
    }

    /// <summary>
    /// 构建原有的媒体内容格式 (用于ChatGPT等不使用模板的提供商)
    /// </summary>
    private string BuildLegacyMediaContent(AiMessageContext context)
    {
        if (!context.MediaDescriptions.Any())
        {
            return context.Content;
        }

        var contentBuilder = new System.Text.StringBuilder();
        contentBuilder.AppendLine("媒体内容:");
        
        for (int i = 0; i < context.MediaDescriptions.Count; i++)
        {
            contentBuilder.AppendLine($"{i + 1}. {context.MediaDescriptions[i]}");
        }

        var mediaType = MediaTypeDescriptions.GetDescription(context.MessageType);
        contentBuilder.AppendLine($"\n请分析以上{mediaType}内容并给出相应的回复。");

        return contentBuilder.ToString();
    }

    /// <summary>
    /// 计算处理时间
    /// </summary>
    private string CalculateProcessTime(AiMessageContext context)
    {
        if (context.ProcessStartTime.HasValue && context.ProcessEndTime.HasValue)
        {
            var duration = context.ProcessEndTime.Value - context.ProcessStartTime.Value;
            return $"{duration.TotalMilliseconds:F0}ms";
        }
        else if (context.ProcessStartTime.HasValue)
        {
            var duration = DateTime.UtcNow - context.ProcessStartTime.Value;
            return $"{duration.TotalMilliseconds:F0}ms (进行中)";
        }
        else
        {
            return "未开始";
        }
    }
}
