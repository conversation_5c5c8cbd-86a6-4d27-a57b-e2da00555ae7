using HappyWechat.Application.DTOs.AiProvider;
using HappyWechat.Domain.ValueObjects;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.Interfaces;

/// <summary>
/// AI消息模板服务接口
/// </summary>
public interface IAiMessageTemplateService
{
    /// <summary>
    /// 根据AI提供商类型构建消息模板
    /// </summary>
    /// <param name="context">消息上下文</param>
    /// <param name="providerType">AI提供商类型</param>
    /// <returns>格式化后的消息内容</returns>
    Task<string> BuildMessageTemplateAsync(AiMessageContext context, AiProviderType providerType);
    

    
    /// <summary>
    /// 构建完整的消息上下文
    /// </summary>
    /// <param name="callbackMessage">微信回调消息</param>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="mediaDescriptions">媒体描述列表</param>
    /// <param name="mediaUrls">媒体URL列表</param>
    /// <returns>消息上下文</returns>
    Task<AiMessageContext> BuildMessageContextAsync(
        HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessage,
        Guid wxManagerId,
        List<string>? mediaDescriptions = null,
        List<string>? mediaUrls = null);
}
