using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Pomelo.EntityFrameworkCore.MySql;
using HappyWechat.Infrastructure.Redis;
using HappyWechat.Infrastructure.Database;
using HappyWechat.Infrastructure.ServiceRegistration;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.Notifications;
using HappyWechat.Infrastructure.Notifications.Interfaces;

namespace HappyWechat.Infrastructure.ServiceRegistration;

/// <summary>
/// 现代化服务注册管理器 - 替换所有旧的服务注册文件
/// 特性：自动发现、依赖验证、生命周期管理、性能监控
/// </summary>
public sealed class ModernServiceRegistry
{
    private readonly IServiceCollection _services;
    private readonly ILogger<ModernServiceRegistry> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<Type, ServiceRegistrationInfo> _registrations = new();
    private readonly HashSet<Type> _discoveredTypes = new();
    private readonly ServiceLifecycleManager _lifecycleManager;

    public ModernServiceRegistry(IServiceCollection services, ILogger<ModernServiceRegistry> logger, IConfiguration configuration)
    {
        _services = services ?? throw new ArgumentNullException(nameof(services));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        var loggerFactory = _services.BuildServiceProvider().GetService<ILoggerFactory>();
        var lifecycleLogger = loggerFactory?.CreateLogger<ServiceLifecycleManager>();
        _lifecycleManager = new ServiceLifecycleManager(lifecycleLogger ?? throw new InvalidOperationException("无法创建ServiceLifecycleManager日志记录器"));
    }

    /// <summary>
    /// 批量注册所有基础设施服务
    /// </summary>
    public ModernServiceRegistry RegisterInfrastructureServices()
    {
        // 🔧 注释冗余的服务注册开始日志 - 减少日志噪音
        // _logger.LogInformation("开始注册基础设施服务");

        // 注册Redis服务（必须在缓存服务之前）
        RegisterRedisServices();
        
        // 注册核心管理器服务
        RegisterCoreManagers();
        
        // 注册缓存服务
        RegisterCacheServices();
        
        // 注册消息队列服务
        RegisterMessageQueueServices();
        
        // 注册数据库服务
        RegisterDatabaseServices();
        
        // 注册AI服务
        RegisterAiServices();
        
        // 注册微信服务
        RegisterWechatServices();

        // 🔧 注册统一通知服务
        RegisterNotificationServices();

        // 注册EYun服务
        RegisterEYunServices();

        // 注册监控服务
        RegisterMonitoringServices();

        // 🔧 注释冗余的服务注册完成日志 - 减少日志噪音
        // _logger.LogInformation("基础设施服务注册完成");
        return this;
    }

    /// <summary>
    /// 批量注册Web层服务
    /// </summary>
    public ModernServiceRegistry RegisterWebServices()
    {
        // 🔧 注释冗余的Web层服务注册开始日志 - 减少日志噪音
        // _logger.LogInformation("开始注册Web层服务");

        // 注册认证授权服务
        RegisterAuthenticationServices();
        
        // 注册API服务
        RegisterApiServices();
        
        // 注册Blazor服务  
        RegisterBlazorServices();
        
        // 注册SignalR服务
        RegisterSignalRServices();

        // 🔧 注释冗余的Web层服务注册完成日志 - 减少日志噪音
        // _logger.LogInformation("Web层服务注册完成");
        return this;
    }

    /// <summary>
    /// 自动发现并注册所有服务
    /// </summary>
    public ModernServiceRegistry DiscoverAndRegisterServices(params Assembly[] assemblies)
    {
        _logger.LogInformation("开始自动服务发现，程序集数量: {AssemblyCount}", assemblies.Length);

        foreach (var assembly in assemblies)
        {
            DiscoverServicesInAssembly(assembly);
        }

        ValidateDependencies();
        RegisterDiscoveredServices();

        _logger.LogInformation("服务发现完成，发现服务: {ServiceCount}", _discoveredTypes.Count);
        return this;
    }

    /// <summary>
    /// 手动注册服务（用于特殊情况）
    /// </summary>
    public ModernServiceRegistry RegisterService<TInterface, TImplementation>(ServiceLifetime lifetime = ServiceLifetime.Scoped)
        where TImplementation : class, TInterface
        where TInterface : class
    {
        var serviceType = typeof(TInterface);
        var implementationType = typeof(TImplementation);

        var registration = new ServiceRegistrationInfo
        {
            ServiceType = serviceType,
            ImplementationType = implementationType,
            Lifetime = lifetime,
            Source = ServiceRegistrationSource.Manual
        };

        _registrations.TryAdd(serviceType, registration);
        RegisterSingleService(registration);

        _logger.LogDebug("手动注册服务: {ServiceType} -> {ImplementationType} ({Lifetime})",
            serviceType.Name, implementationType.Name, lifetime);

        return this;
    }

    /// <summary>
    /// 注册条件服务（仅在条件满足时注册）
    /// </summary>
    public ModernServiceRegistry RegisterConditional<TInterface, TImplementation>(
        Func<IServiceProvider, bool> condition,
        ServiceLifetime lifetime = ServiceLifetime.Scoped)
        where TImplementation : class, TInterface
        where TInterface : class
    {
        _services.Add(ServiceDescriptor.Describe(typeof(TInterface), provider =>
        {
            if (condition(provider))
            {
                return ActivatorUtilities.CreateInstance<TImplementation>(provider);
            }
            throw new InvalidOperationException($"条件不满足，无法创建服务 {typeof(TInterface).Name}");
        }, lifetime));

        _logger.LogDebug("注册条件服务: {ServiceType} -> {ImplementationType}",
            typeof(TInterface).Name, typeof(TImplementation).Name);

        return this;
    }

    /// <summary>
    /// 注册装饰器服务
    /// </summary>
    public ModernServiceRegistry RegisterDecorator<TInterface, TDecorator>(ServiceLifetime lifetime = ServiceLifetime.Scoped)
        where TDecorator : class, TInterface
        where TInterface : class
    {
        _services.Decorate<TInterface, TDecorator>();
        _logger.LogDebug("注册装饰器服务: {ServiceType} -> {DecoratorType}",
            typeof(TInterface).Name, typeof(TDecorator).Name);
        return this;
    }

    /// <summary>
    /// 验证所有服务的完整性
    /// </summary>
    public ServiceValidationReport ValidateServices()
    {
        var report = new ServiceValidationReport();
        var serviceProvider = _services.BuildServiceProvider();

        foreach (var registration in _registrations.Values)
        {
            try
            {
                var service = serviceProvider.GetService(registration.ServiceType);
                if (service == null)
                {
                    report.AddError(registration.ServiceType, "服务无法解析");
                }
                else
                {
                    report.AddSuccess(registration.ServiceType);
                }
            }
            catch (Exception ex)
            {
                report.AddError(registration.ServiceType, ex.Message);
            }
        }

        serviceProvider.Dispose();
        return report;
    }

    /// <summary>
    /// 获取服务注册统计信息
    /// </summary>
    public ServiceRegistrationStatistics GetStatistics()
    {
        return new ServiceRegistrationStatistics
        {
            TotalServices = _registrations.Count,
            SingletonCount = _registrations.Values.Count(r => r.Lifetime == ServiceLifetime.Singleton),
            ScopedCount = _registrations.Values.Count(r => r.Lifetime == ServiceLifetime.Scoped),
            TransientCount = _registrations.Values.Count(r => r.Lifetime == ServiceLifetime.Transient),
            AutoDiscoveredCount = _registrations.Values.Count(r => r.Source == ServiceRegistrationSource.AutoDiscovered),
            ManualCount = _registrations.Values.Count(r => r.Source == ServiceRegistrationSource.Manual)
        };
    }

    #region 具体服务注册方法

    private void RegisterCoreManagers()
    {
        // 注册现代化核心管理器
        _services.AddSingleton<ServiceLifecycleManager>();
        _services.AddSingleton<HappyWechat.Infrastructure.Caching.Interfaces.IUnifiedCacheManager, HappyWechat.Infrastructure.Caching.UnifiedCacheManager>();
        _services.AddSingleton<HappyWechat.Infrastructure.Configuration.ISmartConfigurationManager, HappyWechat.Infrastructure.Configuration.SmartConfigurationManager>();
        
        // 注册配置变更通知服务
        _services.AddScoped<HappyWechat.Infrastructure.Configuration.IConfigurationChangeNotifier, HappyWechat.Infrastructure.Configuration.RedisConfigurationChangeNotifier>();

        // 🚀 注册配置一致性服务 - 修复WxContact页面依赖注入问题
        _services.AddScoped<HappyWechat.Infrastructure.Configuration.IConfigurationConsistencyService,
            HappyWechat.Infrastructure.Configuration.ConfigurationConsistencyService>();

        // 🚀 注册配置生效跟踪器 - 修复WxContact页面依赖注入问题
        _services.AddScoped<HappyWechat.Infrastructure.Configuration.IConfigurationEffectivenessTracker,
            HappyWechat.Infrastructure.Configuration.ConfigurationEffectivenessTracker>();

        // 🚀 注册统一配置管理器 - 修复WxContact页面依赖注入问题
        _services.AddScoped<HappyWechat.Infrastructure.Configuration.IUnifiedConfigurationManager,
            HappyWechat.Infrastructure.Configuration.UnifiedConfigurationManager>();
        _services.AddScoped<HappyWechat.Infrastructure.Configuration.IUnifiedConfigManager,
            HappyWechat.Infrastructure.Configuration.UnifiedConfigManager>();

        // 注册系统配置管理器
        _services.AddScoped<HappyWechat.Application.Interfaces.ISystemConfigManager, HappyWechat.Infrastructure.SystemConfig.DatabaseSystemConfigManager>();
        _services.AddScoped<HappyWechat.Infrastructure.SystemConfig.ISystemConfigDataService, HappyWechat.Infrastructure.SystemConfig.SystemConfigDataService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.ISystemConfigRepository, HappyWechat.Infrastructure.Identity.Repositories.SystemConfigRepository>();
        
        _logger.LogDebug("核心管理器服务注册完成");
    }

    private void RegisterRedisServices()
    {
        // 注册Redis服务
        _services.AddRedisServices(_configuration);
        
        _logger.LogDebug("Redis服务注册完成");
    }

    private void RegisterCacheServices()
    {
        // 注册缓存相关服务（IUnifiedCacheManager已在RegisterCoreManagers中注册）
        _services.AddScoped<HappyWechat.Infrastructure.Caching.IContactBatchCacheService, HappyWechat.Infrastructure.Caching.ContactBatchCacheService>();
        _services.AddScoped<HappyWechat.Infrastructure.Caching.CacheInvalidationService>();
        _services.AddScoped<HappyWechat.Infrastructure.Caching.Interfaces.IUnifiedRedisCacheService, HappyWechat.Infrastructure.Caching.UnifiedRedisCacheService>();

        // 注册统一缓存服务（兼容性接口）
        _services.AddScoped<HappyWechat.Infrastructure.Caching.Interfaces.IUnifiedCacheService, HappyWechat.Infrastructure.Caching.UnifiedCacheServiceAdapter>();

        // 🔧 注册WxManagerId缓存服务 - 用于WId自动获取功能
        _services.AddScoped<HappyWechat.Infrastructure.Caching.IWxManagerIdCacheService, HappyWechat.Infrastructure.Caching.WxManagerIdCacheService>();

        // 🚀 注册统一ID管理器 - 高性能ID转换服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IUnifiedIdManager, 
            HappyWechat.Infrastructure.IdManagement.UnifiedIdManager>();

        // 🛡️ 注册ID映射降级解析器
        _services.AddScoped<HappyWechat.Infrastructure.IdManagement.IFallbackIdResolver,
            HappyWechat.Infrastructure.IdManagement.FallbackIdResolver>();

        // 📊 注册ID映射健康监控服务
        _services.AddHostedService<HappyWechat.Infrastructure.IdManagement.IdMappingHealthService>();

        // 注册通知服务
        _services.AddScoped<HappyWechat.Infrastructure.Notifications.IUnifiedRedisNotificationService, HappyWechat.Infrastructure.Notifications.UnifiedRedisNotificationService>();

        _logger.LogDebug("缓存服务注册完成");
    }

    private void RegisterMessageQueueServices()
    {
        // 🚀 注册简化服务架构 - 包含所有必要的业务服务（包括消费者）
        _services.AddSimplifiedMessageQueue();

        // 注册集成服务
        _services.AddScoped<HappyWechat.Infrastructure.Integration.IUnifiedArchitectureIntegrationService,
            HappyWechat.Infrastructure.Integration.UnifiedArchitectureIntegrationService>();

        _logger.LogDebug("消息队列服务注册完成");
    }

    private void RegisterDatabaseServices()
    {
        // 注册时区服务
        _services.AddScoped<HappyWechat.Infrastructure.Common.ITimeZoneService, HappyWechat.Infrastructure.Common.TimeZoneService>();
        
        // 🚀 修复数据库配置 - 添加标准DbContext配置（Identity系统必需）
        var connectionString = _configuration.GetConnectionString("DefaultConnection") 
            ?? _configuration["Database:WriteConnectionString"]
            ?? throw new InvalidOperationException("数据库连接字符串未配置");
            
        _services.AddDbContext<HappyWechat.Infrastructure.Identity.Repositories.ApplicationDbContext>(options =>
        {
            options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mysqlOptions =>
            {
                mysqlOptions.CommandTimeout(30);
                mysqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);
            });
            
            // 开发环境启用详细错误和敏感数据日志
            if (_configuration.GetValue<bool>("Database:EnableDetailedErrors", false))
            {
                options.EnableDetailedErrors();
            }
            
            if (_configuration.GetValue<bool>("Database:EnableSensitiveDataLogging", false))
            {
                options.EnableSensitiveDataLogging();
            }
        });
        
        // 注册数据库相关服务 - 使用独立实现避免生命周期冲突
        _services.AddScoped<HappyWechat.Infrastructure.Database.IDbContextFactory, 
            HappyWechat.Infrastructure.Database.IndependentDbContextFactory>();
        _services.AddScoped<HappyWechat.Infrastructure.Database.IQueryOptimizer, 
            HappyWechat.Infrastructure.Database.QueryOptimizer>();
            
        // 🚀 注册EF Core标准IDbContextFactory适配器 - 解决Repository依赖问题
        _services.AddScoped<Microsoft.EntityFrameworkCore.IDbContextFactory<HappyWechat.Infrastructure.Identity.Repositories.ApplicationDbContext>, 
            HappyWechat.Infrastructure.Database.EfCoreDbContextFactoryAdapter>();
        
        // 🔧 移除冲突的AddDbContextFactory配置 - 避免与AddDbContext产生生命周期冲突
        // Identity系统需要AddDbContext (Scoped)，而AddDbContextFactory创建Singleton服务
        // 这两者不能共存，会导致"Cannot consume scoped service from singleton"错误
        
        // 🔧 移除池化DbContext配置 - 与Identity系统的AddDbContext不兼容
        // 池化DbContext会创建IDbContextPool<T> (Singleton)，但它依赖DbContextOptions<T> (Scoped)
        // 这会导致"Cannot consume scoped service from singleton"错误
        // 改用IndependentDbContextFactory提供高性能数据库访问
        
        // 🚀 注册高性能独立数据库服务（替代池化方案）
        _services.AddScoped<HappyWechat.Infrastructure.Database.IHighPerformanceDbService, 
            HappyWechat.Infrastructure.Database.HighPerformanceDbService>();
        
        // 注册文件存储服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IFileStorageService, HappyWechat.Infrastructure.FileStorage.FileStorageService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IUnifiedStorageService, HappyWechat.Infrastructure.FileStorage.UnifiedStorageService>();

        // 🚀 注册文件存储提供者 - 修复MinIOFileStorageProvider未注册的问题
        _services.AddScoped<HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider>();
        _services.AddScoped<HappyWechat.Infrastructure.FileStorage.TencentCOSFileStorageProvider>();
        
        _logger.LogDebug("数据库服务注册完成 - 连接字符串: {ConnectionString}, 池化DbContext: 已启用", 
            connectionString.Substring(0, Math.Min(50, connectionString.Length)) + "...");
    }

    private void RegisterAiServices()
    {
        // 注册AI相关服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IAiProviderFactory, 
            HappyWechat.Infrastructure.AiProvider.AiProviderFactory>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IAiAgentService, 
            HappyWechat.Infrastructure.AiAgent.AiAgentService>();
        _services.AddScoped<HappyWechat.Infrastructure.AiAgent.IAiAgentRepository, 
            HappyWechat.Infrastructure.AiAgent.AiAgentRepository>();
        _services.AddHttpClient<HappyWechat.Infrastructure.AiAgent.AiTestHttpClient>();
        _services.AddScoped<HappyWechat.Infrastructure.AiAgent.AiRequestLogger>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IAiConfigHotReloadService,
            HappyWechat.Infrastructure.AiConfig.AiConfigHotReloadService>();

        // 🚀 注册增强AI测试服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IEnhancedAiTestService,
            HappyWechat.Infrastructure.AiAgent.EnhancedAiTestService>();

        // 🚀 注册全局AI配置服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IGlobalAiConfigService,
            HappyWechat.Infrastructure.SystemConfig.GlobalAiConfigService>();

        // 🔧 注册AI响应处理器
        _services.AddScoped<HappyWechat.Application.Interfaces.IAiResponseProcessor,
            HappyWechat.Infrastructure.Services.AiResponseProcessor>();

        _logger.LogDebug("AI服务注册完成");
    }

    private void RegisterWechatServices()
    {
        // 注册微信相关服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IWxService,
            HappyWechat.Infrastructure.Wx.WxService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IWxContactService,
            HappyWechat.Infrastructure.Wx.WxContactService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IWxGroupService,
            HappyWechat.Infrastructure.Wx.SimplifiedWxGroupService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IWxContactCacheService,
            HappyWechat.Infrastructure.Wx.WxContactCacheService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IWxMessageService,
            HappyWechat.Infrastructure.Wx.WxMessageService>();
        _services.AddScoped<HappyWechat.Infrastructure.Wx.IWxContactQueryOptimizer,
            HappyWechat.Infrastructure.Wx.WxContactQueryOptimizer>();

        // 注册联系人同步相关服务
        _services.AddScoped<HappyWechat.Infrastructure.Services.ContactSyncDelayService>();
        _services.AddScoped<HappyWechat.Infrastructure.Services.ContactBatchProcessor>();
        _services.AddScoped<HappyWechat.Infrastructure.Services.ContactSyncProgressTracker>();

        // 🔧 注册群组同步相关服务
        _services.AddScoped<HappyWechat.Infrastructure.Services.GroupSyncProgressTracker>();

        // 注册联系人映射服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IContactMappingService,
            HappyWechat.Infrastructure.Mapping.ContactMappingService>();

        // 注册微信管理器仓储
        _services.AddScoped<HappyWechat.Application.Interfaces.IWxManagerRepository,
            HappyWechat.Infrastructure.Wx.WxManagerRepository>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IWxContactListRepository,
            HappyWechat.Infrastructure.Wx.WxContactListRepository>();

        // 🚀 注册真正的群组同步服务
        _services.AddScoped<HappyWechat.Infrastructure.Wx.WxGroupSyncService>();
        
        // EYun服务已在EYunServiceRegistrationExtensions中统一注册，避免重复注册
        
        // 注册音频转换配置选项
        _services.Configure<HappyWechat.Application.Options.AudioConverterOptions>(_configuration.GetSection("AudioConverter"));
        
        // 注册音频转换服务
        _services.AddScoped<HappyWechat.Infrastructure.Audio.ISilkAudioConverter, 
            HappyWechat.Infrastructure.Audio.SilkAudioConverter>();
        _services.AddScoped<HappyWechat.Infrastructure.Audio.IAudioConversionService, 
            HappyWechat.Infrastructure.Audio.AudioConversionService>();
        
        // 注册Material相关服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IMaterialService, 
            HappyWechat.Infrastructure.Material.MaterialService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IMaterialCategoryService, 
            HappyWechat.Infrastructure.Material.MaterialCategoryService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IMaterialRepository, 
            HappyWechat.Infrastructure.Material.MaterialRepository>();
        
        // 🔧 注册敏感词检测服务 - 修复EYunCallbackProcessor依赖注入问题
        _services.AddScoped<HappyWechat.Infrastructure.Services.IEnhancedSensitiveWordService, 
            HappyWechat.Infrastructure.Services.EnhancedSensitiveWordService>();
        
        // 注册媒体处理相关服务
        _services.AddScoped<HappyWechat.Infrastructure.MediaProcessing.IUnifiedMediaProcessor, 
            HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor>();
        
        // 注册媒体处理依赖服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IFileUploadService, 
            HappyWechat.Infrastructure.MessageProcess.FileUploadService>();
        _services.AddScoped<HappyWechat.Infrastructure.Services.IUrlCleaningService, 
            HappyWechat.Infrastructure.Services.UrlCleaningService>();
        _services.AddScoped<HappyWechat.Infrastructure.Services.IMediaFailureNotificationService, 
            HappyWechat.Infrastructure.Services.MediaFailureNotificationService>();
        
        // 注册消息分割服务
        _services.Configure<HappyWechat.Infrastructure.MessageSplitting.MessageSplitterOptions>(_configuration.GetSection("MessageSplitter"));
        _services.AddScoped<HappyWechat.Infrastructure.MessageSplitting.IAdvancedMessageSplitter, 
            HappyWechat.Infrastructure.MessageSplitting.AdvancedMessageSplitter>();
        
        // 注册WxManagerId缓存服务
        _services.AddScoped<HappyWechat.Infrastructure.Caching.IWxManagerIdCacheService, 
            HappyWechat.Infrastructure.Caching.WxManagerIdCacheService>();
        
        _logger.LogDebug("微信服务注册完成");
    }

    private void RegisterMonitoringServices()
    {
        // 注册监控服务
        _services.AddSingleton<HappyWechat.Infrastructure.Monitoring.ComprehensiveHealthCheck>();
        _services.AddHostedService<ServiceHealthValidator>();
        _services.AddSingleton<HappyWechat.Infrastructure.Resilience.SmartCircuitBreaker>();
        _services.AddScoped<HappyWechat.Infrastructure.Monitoring.ModernPerformanceMonitor>();
        
        _logger.LogDebug("监控服务注册完成");
    }

    private void RegisterEYunServices()
    {
        // 调用EYun服务注册扩展
        _services.AddEYunServices(_configuration);

        _logger.LogDebug("EYun服务注册完成");
    }

    private void RegisterAuthenticationServices()
    {
        // 注册认证授权服务 - 使用Blazor兼容的实现
        _services.AddScoped<HappyWechat.Application.Interfaces.ICurrentUserService,
            HappyWechat.Infrastructure.Services.CurrentUserService>();
        
        // 注册认证服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IAuthService, 
            HappyWechat.Infrastructure.Auth.AuthService>();
        
        // 注册Redis认证配置选项
        _services.Configure<HappyWechat.Infrastructure.Auth.RedisAuthenticationOptions>(
            _configuration.GetSection(HappyWechat.Infrastructure.Auth.RedisAuthenticationOptions.SectionName));

        // 注册Redis认证服务
        _services.AddScoped<HappyWechat.Infrastructure.Auth.IRedisSessionManager, HappyWechat.Infrastructure.Auth.RedisSessionManager>();
        _services.AddScoped<HappyWechat.Infrastructure.Auth.IRedisAuthenticationService, HappyWechat.Infrastructure.Auth.RedisAuthenticationService>();

        // 注册SessionId获取策略
        _services.AddScoped<HappyWechat.Infrastructure.Auth.MemoryCacheSessionIdStrategy>();
        _services.AddScoped<HappyWechat.Infrastructure.Auth.HttpContextSessionIdStrategy>();
        _services.AddScoped<HappyWechat.Infrastructure.Auth.LocalStorageSessionIdStrategy>();
        _services.AddScoped<HappyWechat.Infrastructure.Auth.LegacyProviderSessionIdStrategy>();

        // 注册SessionId获取策略集合
        _services.AddScoped<IEnumerable<HappyWechat.Infrastructure.Auth.SessionIdRetrievalStrategy>>(provider =>
        {
            return new HappyWechat.Infrastructure.Auth.SessionIdRetrievalStrategy[]
            {
                provider.GetRequiredService<HappyWechat.Infrastructure.Auth.MemoryCacheSessionIdStrategy>(),
                provider.GetRequiredService<HappyWechat.Infrastructure.Auth.HttpContextSessionIdStrategy>(),
                provider.GetRequiredService<HappyWechat.Infrastructure.Auth.LocalStorageSessionIdStrategy>(),
                provider.GetRequiredService<HappyWechat.Infrastructure.Auth.LegacyProviderSessionIdStrategy>()
            };
        });

        // 注册SessionId获取服务
        _services.AddScoped<HappyWechat.Infrastructure.Auth.ISessionIdRetrievalService,
            HappyWechat.Infrastructure.Auth.SessionIdRetrievalService>();

        // 注册统一认证状态提供者 (替代Redis认证状态提供者)
        _services.AddScoped<Microsoft.AspNetCore.Components.Authorization.AuthenticationStateProvider,
            HappyWechat.Infrastructure.Auth.UnifiedAuthenticationStateProvider>();

        // 注册统一认证状态提供者接口
        _services.AddScoped<HappyWechat.Infrastructure.Auth.IUnifiedAuthenticationStateProvider>(provider =>
            (HappyWechat.Infrastructure.Auth.UnifiedAuthenticationStateProvider)provider.GetRequiredService<Microsoft.AspNetCore.Components.Authorization.AuthenticationStateProvider>());

        // 保持向后兼容 - 注册Redis认证状态提供者作为备用
        _services.AddScoped<HappyWechat.Infrastructure.Auth.RedisAuthenticationStateProvider>();

        // JWT服务已移除 - 现在使用纯SessionId认证

        _logger.LogDebug("Redis认证服务注册完成");
    }

    private void RegisterApiServices()
    {
        // 注册API相关服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IMaterialService,
            HappyWechat.Infrastructure.Material.MaterialService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IMaterialCategoryService,
            HappyWechat.Infrastructure.Material.MaterialCategoryService>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IMaterialRepository,
            HappyWechat.Infrastructure.Material.MaterialRepository>();
        _services.AddScoped<HappyWechat.Application.Interfaces.IMaterialCategoryRepository,
            HappyWechat.Infrastructure.Material.MaterialCategoryRepository>();

        // 🚀 注册定时任务服务
        _services.AddScoped<HappyWechat.Application.Interfaces.IScheduleTaskService,
            HappyWechat.Infrastructure.ScheduledPublish.ScheduleTaskService>();

        _logger.LogDebug("API服务注册完成");
    }

    private void RegisterBlazorServices()
    {
        // Blazor服务在Web层注册，Infrastructure层不直接引用Web层服务
        _logger.LogDebug("Blazor服务注册完成");
    }

    private void RegisterSignalRServices()
    {
        // SignalR服务在Web层注册，Infrastructure层不直接注册具体实现
        // IUnifiedNotificationService将在Web层通过ServiceRegistrationExtensions注册
        _logger.LogDebug("SignalR服务注册完成");
    }

    #endregion

    #region 私有方法

    private void DiscoverServicesInAssembly(Assembly assembly)
    {
        var types = assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract && !t.IsGenericTypeDefinition)
            .ToArray();

        foreach (var type in types)
        {
            // 查找实现的接口
            var interfaces = type.GetInterfaces()
                .Where(i => !i.IsGenericTypeDefinition && 
                           i.Namespace?.StartsWith("HappyWechat") == true)
                .ToArray();

            foreach (var interfaceType in interfaces)
            {
                var lifetime = DetermineServiceLifetime(type, interfaceType);
                var registration = new ServiceRegistrationInfo
                {
                    ServiceType = interfaceType,
                    ImplementationType = type,
                    Lifetime = lifetime,
                    Source = ServiceRegistrationSource.AutoDiscovered
                };

                if (_registrations.TryAdd(interfaceType, registration))
                {
                    _discoveredTypes.Add(type);
                }
            }
        }
    }

    private ServiceLifetime DetermineServiceLifetime(Type implementationType, Type serviceType)
    {
        // 检查是否有ServiceLifetime特性
        var lifetimeAttribute = implementationType.GetCustomAttribute<ServiceLifetimeAttribute>();
        if (lifetimeAttribute != null)
        {
            return lifetimeAttribute.Lifetime;
        }

        // 根据命名约定确定生命周期
        var typeName = implementationType.Name;
        
        if (typeName.Contains("Singleton") || typeName.Contains("Manager") || typeName.Contains("Factory"))
            return ServiceLifetime.Singleton;
        
        if (typeName.Contains("Transient") || typeName.Contains("Helper"))
            return ServiceLifetime.Transient;
        
        // 默认为Scoped
        return ServiceLifetime.Scoped;
    }

    private void ValidateDependencies()
    {
        var dependencyGraph = new Dictionary<Type, HashSet<Type>>();
        
        foreach (var registration in _registrations.Values)
        {
            var dependencies = GetServiceDependencies(registration.ImplementationType);
            dependencyGraph[registration.ServiceType] = dependencies;
        }

        // 检查循环依赖
        foreach (var kvp in dependencyGraph)
        {
            if (HasCircularDependency(kvp.Key, kvp.Value, dependencyGraph, new HashSet<Type>()))
            {
                _logger.LogWarning("检测到循环依赖: {ServiceType}", kvp.Key.Name);
            }
        }
    }

    private HashSet<Type> GetServiceDependencies(Type implementationType)
    {
        var dependencies = new HashSet<Type>();
        var constructors = implementationType.GetConstructors();
        
        foreach (var constructor in constructors)
        {
            var parameters = constructor.GetParameters();
            foreach (var parameter in parameters)
            {
                if (_registrations.ContainsKey(parameter.ParameterType))
                {
                    dependencies.Add(parameter.ParameterType);
                }
            }
        }
        
        return dependencies;
    }

    private bool HasCircularDependency(Type serviceType, HashSet<Type> dependencies,
        Dictionary<Type, HashSet<Type>> dependencyGraph, HashSet<Type> visited)
    {
        if (visited.Contains(serviceType))
            return true;

        visited.Add(serviceType);

        foreach (var dependency in dependencies)
        {
            if (dependencyGraph.TryGetValue(dependency, out var subDependencies))
            {
                if (HasCircularDependency(dependency, subDependencies, dependencyGraph, visited))
                    return true;
            }
        }

        visited.Remove(serviceType);
        return false;
    }

    private void RegisterDiscoveredServices()
    {
        foreach (var registration in _registrations.Values)
        {
            RegisterSingleService(registration);
        }
    }

    private void RegisterSingleService(ServiceRegistrationInfo registration)
    {
        var serviceDescriptor = ServiceDescriptor.Describe(
            registration.ServiceType,
            registration.ImplementationType,
            registration.Lifetime);

        _services.Add(serviceDescriptor);
        _lifecycleManager.TrackService(registration);
    }

    /// <summary>
    /// 注册统一通知服务
    /// </summary>
    private void RegisterNotificationServices()
    {
        try
        {
            _logger.LogDebug("🔔 开始注册统一通知服务...");

            // 注册统一同步通知服务
            _services.AddScoped<IUnifiedSyncNotificationService, UnifiedSyncNotificationService>();

            _logger.LogDebug("✅ 统一通知服务注册完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 统一通知服务注册失败");
            throw;
        }
    }

    #endregion
}

/// <summary>
/// 服务注册信息
/// </summary>
public class ServiceRegistrationInfo
{
    public Type ServiceType { get; set; } = null!;
    public Type ImplementationType { get; set; } = null!;
    public ServiceLifetime Lifetime { get; set; }
    public ServiceRegistrationSource Source { get; set; }
    public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 服务注册来源
/// </summary>
public enum ServiceRegistrationSource
{
    AutoDiscovered,
    Manual,
    Conditional
}

/// <summary>
/// 服务生命周期特性
/// </summary>
[AttributeUsage(AttributeTargets.Class)]
public class ServiceLifetimeAttribute : Attribute
{
    public ServiceLifetime Lifetime { get; }

    public ServiceLifetimeAttribute(ServiceLifetime lifetime)
    {
        Lifetime = lifetime;
    }
}

/// <summary>
/// 服务验证报告
/// </summary>
public class ServiceValidationReport
{
    private readonly List<ServiceValidationResult> _results = new();

    public IReadOnlyList<ServiceValidationResult> Results => _results.AsReadOnly();
    public bool IsValid => _results.All(r => r.IsValid);
    public int SuccessCount => _results.Count(r => r.IsValid);
    public int ErrorCount => _results.Count(r => !r.IsValid);

    public void AddSuccess(Type serviceType)
    {
        _results.Add(new ServiceValidationResult
        {
            ServiceType = serviceType,
            IsValid = true
        });
    }

    public void AddError(Type serviceType, string errorMessage)
    {
        _results.Add(new ServiceValidationResult
        {
            ServiceType = serviceType,
            IsValid = false,
            ErrorMessage = errorMessage
        });
    }
}

/// <summary>
/// 服务验证结果
/// </summary>
public class ServiceValidationResult
{
    public Type ServiceType { get; set; } = null!;
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 服务注册统计信息
/// </summary>
public class ServiceRegistrationStatistics
{
    public int TotalServices { get; set; }
    public int SingletonCount { get; set; }
    public int ScopedCount { get; set; }
    public int TransientCount { get; set; }
    public int AutoDiscoveredCount { get; set; }
    public int ManualCount { get; set; }
}

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加装饰器服务
    /// </summary>
    public static IServiceCollection Decorate<TInterface, TDecorator>(this IServiceCollection services)
        where TDecorator : class, TInterface
        where TInterface : class
    {
        var decorateeDescriptor = services.FirstOrDefault(s => s.ServiceType == typeof(TInterface));
        if (decorateeDescriptor == null)
        {
            throw new InvalidOperationException($"Service of type {typeof(TInterface).Name} is not registered");
        }

        services.Remove(decorateeDescriptor);

        services.Add(ServiceDescriptor.Describe(typeof(TInterface), provider =>
        {
            var decoratee = (TInterface)ActivatorUtilities.CreateInstance(provider, decorateeDescriptor.ImplementationType!);
            return ActivatorUtilities.CreateInstance<TDecorator>(provider, decoratee);
        }, decorateeDescriptor.Lifetime));

        return services;
    }
}