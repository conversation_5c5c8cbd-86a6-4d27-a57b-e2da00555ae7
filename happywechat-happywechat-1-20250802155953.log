[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedWxCallbackConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: wx_callback
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedContactSyncConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: contact_sync
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedGroupSyncConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: group_sync
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedFileSendConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: file_send
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 2.6527ms
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1575ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 2129ms
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedGroupSyncConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: group_sync
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedFileSendConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: file_send
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedContactSyncConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: contact_sync
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedWxCallbackConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: wx_callback
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedGroupSyncConsumer[0]
      🔗 Redis连接检查成功 - QueueType: group_sync, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      🔗 Redis连接检查成功 - QueueType: ai_message, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedContactSyncConsumer[0]
      🔗 Redis连接检查成功 - QueueType: contact_sync, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedWxCallbackConsumer[0]
      🔗 Redis连接检查成功 - QueueType: wx_callback, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedFileSendConsumer[0]
      🔗 Redis连接检查成功 - QueueType: file_send, 发现队列数量: 0
info: HappyWechat.Infrastructure.Auth.RedisSessionManager[0]
      🗑️ 销毁会话成功 - SessionId: c99d647c048b4ac18ca12ec90cef4e1a8DDD1E6560A1A26
info: HappyWechat.Infrastructure.Auth.RedisSessionManager[0]
      ✅ 创建会话成功 - SessionId: 0ff52215ec9e4d08a47d1796f0e526848DDD1EBA856750A, UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, ExpiresAt: 08/03/2025 01:40:22
info: HappyWechat.Infrastructure.Auth.RedisAuthenticationService[0]
      ✅ 用户登录成功 - UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, SessionId: 0ff52215ec9e4d08a47d1796f0e526848DDD1EBA856750A
info: HappyWechat.Infrastructure.Auth.UnifiedAuthenticationStateProvider[0]
      🔔 认证状态变更通知 - SessionId: 0ff52215ec9e4d08a47d1796f0e526848DDD1EBA856750A
info: HappyWechat.Infrastructure.Auth.UnifiedAuthenticationStateProvider[0]
      ✅ 认证状态变更完成 - IsAuthenticated: True
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0d30e076] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0d30e076] ✅ 消息已入队 - QueueType: ai_message, MessageId: 4763fe0c-6745-4196-b58f-ecee5a52c115
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0d30e076] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [26b2bee7] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [32cf6892] 📥 收到EYun回调数据: FromUser: wxid_ic3nmv9anggz22, MessageType: 60999
info: HappyWechat.Web.Controllers.WxController[0]
      [f712135d] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ✅ 找到活跃队列 - QueueName: hw:queue:980a47bc-1452-446c-8e78-72f7bb0c5144:ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      📦 获取到 1 条消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [a7f2956e] 🤖 处理AI消息 - MessageId: 4763fe0c-6745-4196-b58f-ecee5a52c115, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [a7f2956e] 👤 开始处理私聊消息 - FromUser: wxid_scqpt8dyuxqv41, MessageType: 60001
info: HappyWechat.Infrastructure.Services.SimplifiedAiService[0]
      🤖 处理AI消息 - Content: 你好
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - UserId: 00000000-0000-0000-0000-000000000000, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, Content: AI回复: 你好
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 2488478977
info: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [a7f2956e] 🚀 发送wx文本消息成功 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: wxid_scqpt8dyuxqv41, Content: AI回复: 你好
info: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [a7f2956e] ✅ 私聊消息处理完成 - FromUser: wxid_scqpt8dyuxqv41, ContactName: 张涛
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [a7f2956e] ✅ AI消息处理成功 - MessageId: 4763fe0c-6745-4196-b58f-ecee5a52c115
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [f1908506] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, Content: @心 你好, MessageType: 80001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [f1908506] ✅ 消息已入队 - QueueType: ai_message, MessageId: 4e673f82-8a71-4d71-896e-1eb4347f2818
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [f1908506] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [6f481003] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ✅ 找到活跃队列 - QueueName: hw:queue:980a47bc-1452-446c-8e78-72f7bb0c5144:ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      📦 获取到 1 条消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [528b1100] 🤖 处理AI消息 - MessageId: 4e673f82-8a71-4d71-896e-1eb4347f2818, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Content: @心 你好, MessageType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [528b1100] ✅ AI消息处理成功 - MessageId: 4e673f82-8a71-4d71-896e-1eb4347f2818
info: HappyWechat.Web.Controllers.WxMessageController[0]
      用户操作 - Controller: WxMessageController, Operation: SendTextMessage, UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, Username: admin, Details: { WcId = 53451126890@chatroom, Content = 你好 }
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      发送文本消息 - UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, WcId: 53451126890@chatroom, Content: 你好
info: HappyWechat.Infrastructure.Wx.WxMessageService[0]
      文本消息发送成功 - MsgId: 2034152209
info: HappyWechat.Web.Controllers.WxMessageController[0]
      发送文本消息成功 - UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, WcId: 53451126890@chatroom
warn: HappyWechat.Web.Hubs.ContactSyncHub[0]
      SignalR连接异常断开 - UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, ConnectionId: HYzIfwDFVA34IKAsEAH0tg
      System.OperationCanceledException: Client hasn't sent a message/ping within the configured ClientTimeoutInterval.
warn: HappyWechat.Web.Hubs.ContactSyncHub[0]
      SignalR连接异常断开 - UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, ConnectionId: nMBlIXmmPXcMFiB5eXwTAg
      System.OperationCanceledException: Client hasn't sent a message/ping within the configured ClientTimeoutInterval.
warn: HappyWechat.Web.Hubs.ContactSyncHub[0]
      SignalR连接异常断开 - UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, ConnectionId: rDvG0odWXU5NMWdFaTvYNw
      System.OperationCanceledException: Client hasn't sent a message/ping within the configured ClientTimeoutInterval.
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [744aecdf] 📥 收到EYun回调数据: FromGroup: 48916911823@chatroom, Content: 老师明天早上还有课吗，今晚中间有点忙没接上, MessageType: 80001
warn: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [744aecdf] ❌ 8字头群聊消息群组不存在，消息被跳过 - FromGroup: '48916911823@chatroom', MessageType: 80001
info: HappyWechat.Web.Controllers.WxController[0]
      [24d72edf] ✅ 回调处理成功
warn: HappyWechat.Web.Hubs.ContactSyncHub[0]
      SignalR连接异常断开 - UserId: 2a5ea67a-5e54-422f-89e5-0f0fe25285fb, ConnectionId: TSy4zb3s7tainzQFdGrMKA
      System.OperationCanceledException: Client hasn't sent a message/ping within the configured ClientTimeoutInterval.
info: HappyWechat.Web.Components.Common.BaseWxPageComponent[0]
      🔄 群组Tab切换开始 - NewTabIndex: -1
info: HappyWechat.Web.Services.SimplifiedSyncStateManager[0]
      🔄 简化同步状态管理器已释放
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 2ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 8ms
info: HappyWechat.Web.Services.CircuitStateManager[0]
      清理了 1 个过期Circuit
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 7ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 6ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 7ms
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [1c498baf] 📥 收到EYun回调数据: FromUser: wxid_ic3nmv9anggz22, MessageType: 60999
info: HappyWechat.Web.Controllers.WxController[0]
      [53023cc7] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d2303857] 📥 收到EYun回调数据: FromGroup: 48916911823@chatroom, MessageType: 80019
info: HappyWechat.Web.Controllers.WxController[0]
      [fcf15372] ✅ 回调处理成功
info: HappyWechat.Web.Services.CircuitStateManager[0]
      清理了 1 个过期Circuit
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 6ms
