[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedWxCallbackConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: wx_callback
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedContactSyncConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: contact_sync
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedGroupSyncConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: group_sync
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedFileSendConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: file_send
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedTextSendConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: send_text
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedImageSendConsumer[0]
      🚀 简化队列消费者已启动 - QueueType: send_image
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 1.7261ms
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1530ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 2068ms
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedFileSendConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: file_send
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedTextSendConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: send_text
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedImageSendConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: send_image
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedGroupSyncConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: group_sync
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedContactSyncConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: contact_sync
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedWxCallbackConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: wx_callback
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ✅ 简化队列消费者开始处理 - QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedFileSendConsumer[0]
      🔗 Redis连接检查成功 - QueueType: file_send, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedImageSendConsumer[0]
      🔗 Redis连接检查成功 - QueueType: send_image, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedGroupSyncConsumer[0]
      🔗 Redis连接检查成功 - QueueType: group_sync, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      🔗 Redis连接检查成功 - QueueType: ai_message, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedWxCallbackConsumer[0]
      🔗 Redis连接检查成功 - QueueType: wx_callback, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedTextSendConsumer[0]
      🔗 Redis连接检查成功 - QueueType: send_text, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedContactSyncConsumer[0]
      🔗 Redis连接检查成功 - QueueType: contact_sync, 发现队列数量: 0
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e6aec135] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e6aec135] ✅ 消息已入队 - QueueType: ai_message, MessageId: b39cc4c7-82c3-4e3a-9377-5c887e19fea1
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e6aec135] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [71a5cb44] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ✅ 找到活跃队列 - QueueName: hw:queue:980a47bc-1452-446c-8e78-72f7bb0c5144:ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      📦 获取到 1 条消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [449556d8] 🤖 处理AI消息 - MessageId: b39cc4c7-82c3-4e3a-9377-5c887e19fea1, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [449556d8] 👤 开始处理私聊消息 - FromUser: wxid_scqpt8dyuxqv41, MessageType: 60001
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：
      文本内容:
      你好
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: c1baed18-2802-4179-9f35-ae391efcc776
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：
      文本内容:
      你好
fail: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [449556d8] ❌ AI处理异常 - ContactName: 张涛
      System.InvalidOperationException: No service for type 'HappyWechat.Application.Interfaces.IAiResponseProcessor' has been registered.
         at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
         at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
         at HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor.ProcessAiReplyAsync(WxCallbackMessageDto callbackMessage, ContactAiConfigResult configResult, String processingId, CancellationToken cancellationToken)
fail: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [449556d8] ❌ AI消息处理失败 - MessageId: b39cc4c7-82c3-4e3a-9377-5c887e19fea1
info: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      🔄 消息已重新入队 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: ai_message, MessageId: b39cc4c7-82c3-4e3a-9377-5c887e19fea1, RetryCount: 1
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ⚠️ 消息将重试 - MessageId: b39cc4c7-82c3-4e3a-9377-5c887e19fea1, RetryCount: 1, DelayMs: 1000
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      ✅ SessionId获取成功 - 策略: HTTP上下文策略, SessionId: f598dfe8..., 耗时: 2ms
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
⚠️ 注册SignalR刷新回调失败: Could not find 'registerPageRefresh' ('registerPageRefresh' was undefined).
Error: Could not find 'registerPageRefresh' ('registerPageRefresh' was undefined).
    at http://60.204.129.116:15125/_framework/blazor.server.js:1:374
    at Array.forEach (<anonymous>)
    at l.findFunction (http://60.204.129.116:15125/_framework/blazor.server.js:1:342)
    at b (http://60.204.129.116:15125/_framework/blazor.server.js:1:5091)
    at http://60.204.129.116:15125/_framework/blazor.server.js:1:2884
    at new Promise (<anonymous>)
    at w.beginInvokeJSFromDotNet (http://60.204.129.116:15125/_framework/blazor.server.js:1:2847)
    at Jt._invokeClientMethod (http://60.204.129.116:15125/_framework/blazor.server.js:1:63689)
    at Jt._processIncomingData (http://60.204.129.116:15125/_framework/blazor.server.js:1:61078)
    at Jt.connection.onreceive (http://60.204.129.116:15125/_framework/blazor.server.js:1:54719)
info: HappyWechat.Web.Services.ContactAiConfigCacheService[0]
      联系人AI配置缓存失效完成 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Web.Services.ContactAiConfigCacheService[0]
      所有联系人AI配置缓存失效完成
info: HappyWechat.Web.Controllers.WxController[0]
      ✅ 联系人AI配置缓存已清除 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10, ManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      ✅ 联系人AI配置变更通知已记录 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Infrastructure.Integration.UnifiedArchitectureIntegrationService[0]
      Clearing account cache for 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Components.Common.BaseWxPageComponent[0]
      ✅ AI配置更新成功 - Contact: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [42034f24] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [42034f24] ✅ 消息已入队 - QueueType: ai_message, MessageId: 2f4ece91-d434-457d-9caf-434ffdfca10a
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [42034f24] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [12c61ef7] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ✅ 找到活跃队列 - QueueName: hw:queue:980a47bc-1452-446c-8e78-72f7bb0c5144:ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      📦 获取到 1 条消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [31bb3187] 🤖 处理AI消息 - MessageId: 2f4ece91-d434-457d-9caf-434ffdfca10a, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [31bb3187] 👤 开始处理私聊消息 - FromUser: wxid_scqpt8dyuxqv41, MessageType: 60001
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：
      文本内容:
      你好
fail: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [31bb3187] ❌ AI处理异常 - ContactName: 张涛
      System.InvalidOperationException: No service for type 'HappyWechat.Application.Interfaces.IAiResponseProcessor' has been registered.
         at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
         at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
         at HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor.ProcessAiReplyAsync(WxCallbackMessageDto callbackMessage, ContactAiConfigResult configResult, String processingId, CancellationToken cancellationToken)
fail: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [31bb3187] ❌ AI消息处理失败 - MessageId: 2f4ece91-d434-457d-9caf-434ffdfca10a
info: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      🔄 消息已重新入队 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: ai_message, MessageId: 2f4ece91-d434-457d-9caf-434ffdfca10a, RetryCount: 1
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ⚠️ 消息将重试 - MessageId: 2f4ece91-d434-457d-9caf-434ffdfca10a, RetryCount: 1, DelayMs: 1000
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [bfb93b49] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60002
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [bfb93b49] ✅ 消息已入队 - QueueType: ai_message, MessageId: 4a2707a8-f54b-43b7-9a08-67a8a3257e7d
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [bfb93b49] ✅ 回调数据处理完成 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [911c7f3a] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ✅ 找到活跃队列 - QueueName: hw:queue:980a47bc-1452-446c-8e78-72f7bb0c5144:ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      📦 获取到 1 条消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: ai_message
info: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [46e6bb03] 🤖 处理AI消息 - MessageId: 4a2707a8-f54b-43b7-9a08-67a8a3257e7d, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Content: {"original_content":"<?xml version=\"1.0\"?>\n<msg, MessageType: 60002
info: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [46e6bb03] 👤 开始处理私聊消息 - FromUser: wxid_scqpt8dyuxqv41, MessageType: 60002
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：
      文本内容:
      {"original_content":"<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"7ef1a8b12846a6b75d07ecadbbfc657c\" encryver=\"1\" cdnthumbaeskey=\"7ef1a8b12846a6b75d07ecadbbfc657c\" cdnthumburl=\"3057020100044b30490201000204114898ab02032f533f0204d380336f0204688e03d9042436643963663839322d363230662d343033662d613234632d393861346265336531323933020405150a020201000405004c55cf00\" cdnthumblength=\"3886\" cdnthumbheight=\"433\" cdnthumbwidth=\"245\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204114898ab02032f533f0204d380336f0204688e03d9042436643963663839322d363230662d343033662d613234632d393861346265336531323933020405150a020201000405004c55cf00\" length=\"763084\" md5=\"1e75833b8b1cfa14913fbac6d775672b\" hevc_mid_size=\"86302\" originsourcemd5=\"b64599d4d3d6f9f7fb0d40efb191672a\">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUxMzA5MDEwNTAwMDEwMDAiLCJwZHFIYXNoIjoiMGJlMzBkNGJmZDAwZGFiMDg1\nN2VhMDBmODAwNzVhZjUxNWZhMDVlYWQ2M2RlODFkZmQ1ZjA1ODMyOTQxZmZmMCJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n","requires_media_processing":true,"processing_id":"bfb93b49","media_type":"60002"}
fail: HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor[0]
      [46e6bb03] ❌ AI处理异常 - ContactName: 张涛
      System.InvalidOperationException: No service for type 'HappyWechat.Application.Interfaces.IAiResponseProcessor' has been registered.
         at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
         at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
         at HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor.ProcessAiReplyAsync(WxCallbackMessageDto callbackMessage, ContactAiConfigResult configResult, String processingId, CancellationToken cancellationToken)
fail: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      [46e6bb03] ❌ AI消息处理失败 - MessageId: 4a2707a8-f54b-43b7-9a08-67a8a3257e7d
info: HappyWechat.Infrastructure.MessageQueue.Simplified.SimplifiedQueueService[0]
      🔄 消息已重新入队 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, QueueType: ai_message, MessageId: 4a2707a8-f54b-43b7-9a08-67a8a3257e7d, RetryCount: 1
warn: HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers.SimplifiedAiMessageConsumer[0]
      ⚠️ 消息将重试 - MessageId: 4a2707a8-f54b-43b7-9a08-67a8a3257e7d, RetryCount: 1, DelayMs: 1000
