using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;
using System.Text;
using System.Collections.Generic;

namespace HappyWechat.Infrastructure.AiAgent;

/// <summary>
/// AI请求/响应日志记录器
/// </summary>
public class AiRequestLogger
{
    private readonly ILogger<AiRequestLogger> _logger;
    private static readonly Regex TokenRegex = new(@"(token|authorization|bearer)\s*[:=]\s*[""']?([^""'\s,}]+)", RegexOptions.IgnoreCase | RegexOptions.Compiled);

    public AiRequestLogger(ILogger<AiRequestLogger> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 记录AI请求开始
    /// </summary>
    public void LogRequestStart(string providerType, string url, object requestBody, Dictionary<string, string>? headers = null)
    {
        try
        {
            var sanitizedHeaders = SanitizeHeaders(headers);
            var formattedContent = FormatAiRequestContent(providerType, requestBody);

            // 🔧 检测是否是定时任务请求
            if (IsScheduledTaskRequest(requestBody))
            {
                var scheduledContent = ExtractScheduledTaskContent(requestBody);
                _logger.LogInformation("🚀 [{ProviderType}] AI请求开始，定时请求内容： 文本内容: {Content}", providerType, scheduledContent);
            }
            else
            {
                _logger.LogInformation("🚀 [{ProviderType}] AI请求开始，请求内容：{RequestContent}", providerType, formattedContent);
            }
            // _logger.LogInformation("📍 请求URL: {Url}", url); // 已注释以减少日志噪音
            // 简化详细日志输出，降低日志噪音
            // _logger.LogDebug("📋 请求头: {Headers}", JsonConvert.SerializeObject(sanitizedHeaders, Formatting.Indented));
            // _logger.LogDebug("📝 请求体: {RequestBody}", sanitizedBody); // 已合并到上面的日志中
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录AI请求开始日志失败");
        }
    }

    /// <summary>
    /// 记录AI请求成功
    /// </summary>
    public void LogRequestSuccess(string providerType, string url, object responseBody, long elapsedMs, int statusCode = 200)
    {
        try
        {
            var sanitizedResponse = SanitizeResponseBody(responseBody);

            // 🔧 精简AI请求成功日志 - 注释掉分散的多条日志，减少日志噪音
            // _logger.LogInformation("✅ [{ProviderType}] AI请求成功", providerType);
            // _logger.LogInformation("📍 请求URL: {Url}", url);
            // _logger.LogInformation("⏱️ 耗时: {ElapsedMs}ms", elapsedMs);
            // _logger.LogInformation("📊 状态码: {StatusCode}", statusCode);
            // 简化响应体日志，降低日志噪音
            _logger.LogDebug("📄 响应体: {ResponseBody}", sanitizedResponse);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录AI请求成功日志失败");
        }
    }

    /// <summary>
    /// 记录AI请求失败
    /// </summary>
    public void LogRequestFailure(string providerType, string url, string errorMessage, long elapsedMs, int statusCode = 0, string? responseBody = null)
    {
        try
        {
            _logger.LogError("❌ [{ProviderType}] AI请求失败", providerType);
            _logger.LogError("📍 请求URL: {Url}", url);
            _logger.LogError("⏱️ 耗时: {ElapsedMs}ms", elapsedMs);
            _logger.LogError("📊 状态码: {StatusCode}", statusCode);
            _logger.LogError("💥 错误信息: {ErrorMessage}", errorMessage);
            
            if (!string.IsNullOrEmpty(responseBody))
            {
                var sanitizedResponse = SanitizeResponseBody(responseBody);
                _logger.LogError("📄 错误响应: {ResponseBody}", sanitizedResponse);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录AI请求失败日志失败");
        }
    }

    /// <summary>
    /// 记录AI请求异常
    /// </summary>
    public void LogRequestException(string providerType, string url, Exception exception, long elapsedMs)
    {
        try
        {
            _logger.LogError(exception, "💥 [{ProviderType}] AI请求异常", providerType);
            _logger.LogError("📍 请求URL: {Url}", url);
            _logger.LogError("⏱️ 耗时: {ElapsedMs}ms", elapsedMs);
            _logger.LogError("🔍 异常类型: {ExceptionType}", exception.GetType().Name);
            _logger.LogError("📝 异常消息: {ExceptionMessage}", exception.Message);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录AI请求异常日志失败");
        }
    }

    /// <summary>
    /// 脱敏请求头
    /// </summary>
    private Dictionary<string, string> SanitizeHeaders(Dictionary<string, string>? headers)
    {
        if (headers == null) return new Dictionary<string, string>();

        var sanitized = new Dictionary<string, string>();
        foreach (var header in headers)
        {
            if (header.Key.ToLower().Contains("authorization") || header.Key.ToLower().Contains("token"))
            {
                sanitized[header.Key] = MaskSensitiveValue(header.Value);
            }
            else
            {
                sanitized[header.Key] = header.Value;
            }
        }
        return sanitized;
    }

    /// <summary>
    /// 格式化AI请求内容，按照统一格式输出
    /// </summary>
    private string FormatAiRequestContent(string providerType, object requestBody)
    {
        try
        {
            var json = JsonConvert.SerializeObject(requestBody, Formatting.Indented);
            var requestObj = JsonConvert.DeserializeObject<dynamic>(json);

            // 提取消息上下文信息
            string? wid = null, wcId = null, fromGroup = null, fromGroupUser = null, fromGroupUserNickName = null;
            string? messageContent = null;
            List<string> mediaContent = new List<string>();

            // 根据不同AI提供商解析请求格式
            switch (providerType.ToUpper())
            {
                case "MAXKB":
                    if (requestObj?.message != null)
                    {
                        var message = requestObj.message.ToString();
                        var maxkbResult = ParseMaxKBMessage(message);
                        wid = maxkbResult.wid;
                        wcId = maxkbResult.wcId;
                        fromGroup = maxkbResult.fromGroup;
                        fromGroupUser = maxkbResult.fromGroupUser;
                        fromGroupUserNickName = maxkbResult.fromGroupUserNickName;
                        messageContent = maxkbResult.messageContent;
                        mediaContent = maxkbResult.mediaContent;
                    }
                    break;

                case "CHATGPT":
                    if (requestObj?.messages != null)
                    {
                        var chatgptResult = ParseChatGPTMessages(requestObj.messages);
                        wid = chatgptResult.wid;
                        wcId = chatgptResult.wcId;
                        fromGroup = chatgptResult.fromGroup;
                        fromGroupUser = chatgptResult.fromGroupUser;
                        fromGroupUserNickName = chatgptResult.fromGroupUserNickName;
                        messageContent = chatgptResult.messageContent;
                        mediaContent = chatgptResult.mediaContent;
                    }
                    break;

                case "DIFY":
                    if (requestObj?.query != null)
                    {
                        var query = requestObj.query.ToString();
                        var difyResult = ParseDifyQuery(query);
                        wid = difyResult.wid;
                        wcId = difyResult.wcId;
                        fromGroup = difyResult.fromGroup;
                        fromGroupUser = difyResult.fromGroupUser;
                        fromGroupUserNickName = difyResult.fromGroupUserNickName;
                        messageContent = difyResult.messageContent;
                        mediaContent = difyResult.mediaContent;
                    }
                    break;

                case "COZE":
                    if (requestObj?.parameters?.message != null)
                    {
                        var message = requestObj.parameters.message.ToString();
                        var cozeResult = ParseCoZeMessage(message);
                        wid = cozeResult.wid;
                        wcId = cozeResult.wcId;
                        fromGroup = cozeResult.fromGroup;
                        fromGroupUser = cozeResult.fromGroupUser;
                        fromGroupUserNickName = cozeResult.fromGroupUserNickName;
                        messageContent = cozeResult.messageContent;
                        mediaContent = cozeResult.mediaContent;
                    }
                    break;
            }

            // 🎯 构建标准格式的AI请求内容输出
            var parts = new List<string>();
            if (!string.IsNullOrEmpty(wid)) parts.Add($"Wid: {wid}");
            if (!string.IsNullOrEmpty(wcId)) parts.Add($"WcId: {wcId}");
            if (!string.IsNullOrEmpty(fromGroup)) parts.Add($"FromGroup: {fromGroup}");
            if (!string.IsNullOrEmpty(fromGroupUser)) parts.Add($"FromGroupUser: {fromGroupUser}");
            if (!string.IsNullOrEmpty(fromGroupUserNickName)) parts.Add($"FromGroupUserNickName: {fromGroupUserNickName}");

            var result = string.Join(", ", parts);

            if (mediaContent.Any())
            {
                result += "\n媒体内容:";
                for (int i = 0; i < mediaContent.Count; i++)
                {
                    result += $"\n{i + 1}. {mediaContent[i]}";
                }
            }
            else if (!string.IsNullOrEmpty(messageContent))
            {
                result += $"\n文本内容: {messageContent}";
            }

            return result;
        }
        catch (Exception ex)
        {
            // 🔧 增强异常处理：根据不同AI提供商尝试不同的解析策略
            try
            {
                var json = JsonConvert.SerializeObject(requestBody);
                var jsonObj = JObject.Parse(json);

                // 根据AI提供商类型尝试不同的字段提取策略
                switch (providerType.ToUpper())
                {
                    case "CHATGPT":
                        // ChatGPT: 尝试从messages数组中提取最后一个user消息
                        var messages = jsonObj["messages"];
                        if (messages != null && messages.Type == JTokenType.Array)
                        {
                            var userMessages = messages.Where(m => m["role"]?.ToString() == "user").ToList();
                            if (userMessages.Any())
                            {
                                var lastUserMessage = userMessages.Last();
                                var content = lastUserMessage["content"]?.ToString();
                                if (!string.IsNullOrEmpty(content))
                                {
                                    return content; // ChatGPT直接返回原文
                                }
                            }
                        }
                        break;

                    case "DIFY":
                    case "COZE":
                    case "MAXKB":
                    default:
                        // 其他AI提供商: 直接返回message内容（已经是简化格式）
                        var message = jsonObj["message"]?.ToString() ??
                                     jsonObj["query"]?.ToString() ??
                                     jsonObj["parameters"]?["message"]?.ToString();
                        if (!string.IsNullOrEmpty(message))
                        {
                            return message; // 直接返回简化格式的模板内容
                        }
                        break;
                }

                // 如果所有特定策略都失败，尝试通用字段
                var genericMessage = jsonObj["message"]?.ToString() ??
                                   jsonObj["content"]?.ToString() ??
                                   jsonObj["text"]?.ToString();

                if (!string.IsNullOrEmpty(genericMessage))
                {
                    return genericMessage; // 直接返回内容
                }
            }
            catch
            {
                // 忽略二次解析异常
            }

            // 最后的fallback：返回带AI提供商信息的错误信息
            return $"[{providerType}] AI请求内容解析失败 - 原因: {ex.Message}";
        }
    }

    /// <summary>
    /// 检测是否是定时任务请求
    /// </summary>
    private bool IsScheduledTaskRequest(object requestBody)
    {
        try
        {
            var json = JsonConvert.SerializeObject(requestBody);
            var jsonObj = JObject.Parse(json);

            // 检查是否包含定时任务的特征
            // 1. 检查message字段是否不包含Wid、WcId等微信消息特征
            var message = jsonObj["message"]?.ToString() ?? "";

            // 如果message不包含微信消息的特征字段，很可能是定时任务
            return !message.Contains("Wid:") &&
                   !message.Contains("WcId:") &&
                   !message.Contains("FromUser:") &&
                   !message.Contains("FromGroup:") &&
                   !string.IsNullOrEmpty(message);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 提取定时任务内容
    /// </summary>
    private string ExtractScheduledTaskContent(object requestBody)
    {
        try
        {
            var json = JsonConvert.SerializeObject(requestBody);
            var jsonObj = JObject.Parse(json);

            // 提取message字段作为定时任务内容
            var message = jsonObj["message"]?.ToString() ?? "";
            return message.Trim();
        }
        catch
        {
            return "定时任务内容解析失败";
        }
    }

    /// <summary>
    /// 脱敏请求体
    /// </summary>
    private string SanitizeRequestBody(object requestBody)
    {
        try
        {
            var json = JsonConvert.SerializeObject(requestBody, Formatting.Indented);
            return TokenRegex.Replace(json, match =>
            {
                var key = match.Groups[1].Value;
                var value = match.Groups[2].Value;
                return $"{key}: \"{MaskSensitiveValue(value)}\"";
            });
        }
        catch
        {
            return requestBody?.ToString() ?? "null";
        }
    }

    /// <summary>
    /// 脱敏响应体
    /// </summary>
    private string SanitizeResponseBody(object responseBody)
    {
        try
        {
            if (responseBody is string str)
            {
                return TokenRegex.Replace(str, match =>
                {
                    var key = match.Groups[1].Value;
                    var value = match.Groups[2].Value;
                    return $"{key}: \"{MaskSensitiveValue(value)}\"";
                });
            }
            
            var json = JsonConvert.SerializeObject(responseBody, Formatting.Indented);
            return TokenRegex.Replace(json, match =>
            {
                var key = match.Groups[1].Value;
                var value = match.Groups[2].Value;
                return $"{key}: \"{MaskSensitiveValue(value)}\"";
            });
        }
        catch
        {
            return responseBody?.ToString() ?? "null";
        }
    }

    /// <summary>
    /// 掩码敏感值
    /// </summary>
    private string MaskSensitiveValue(string value)
    {
        if (string.IsNullOrEmpty(value)) return value;
        if (value.Length <= 8) return "***";

        return value.Substring(0, 4) + "***" + value.Substring(value.Length - 4);
    }

    /// <summary>
    /// 解析MaxKB消息格式
    /// </summary>
    private (string? wid, string? wcId, string? fromGroup, string? fromGroupUser, string? fromGroupUserNickName, string? messageContent, List<string> mediaContent) ParseMaxKBMessage(string message)
    {
        try
        {
            string? wid = null, wcId = null, fromGroup = null, fromGroupUser = null, fromGroupUserNickName = null;
            string? messageContent = null;
            List<string> mediaContent = new List<string>();

            // 🔧 修复：支持逗号分隔的单行格式和多行格式
            if (message.Contains("Wid:") && message.Contains(","))
            {
                // 处理逗号分隔的单行格式：Wid: xxx,WcId:xxx,FromUser:xxx,FromUserNickName:xxx,文本内容:\n内容
                var parts = message.Split(',');
                foreach (var part in parts)
                {
                    var trimmedPart = part.Trim();
                    if (trimmedPart.StartsWith("Wid:")) wid = ExtractValue(trimmedPart, "Wid:");
                    else if (trimmedPart.StartsWith("WcId:")) wcId = ExtractValue(trimmedPart, "WcId:");
                    else if (trimmedPart.StartsWith("FromGroup:")) fromGroup = ExtractValue(trimmedPart, "FromGroup:");
                    else if (trimmedPart.StartsWith("FromGroupUser:")) fromGroupUser = ExtractValue(trimmedPart, "FromGroupUser:");
                    else if (trimmedPart.StartsWith("FromGroupUserNickName:")) fromGroupUserNickName = ExtractValue(trimmedPart, "FromGroupUserNickName:");
                    else if (trimmedPart.StartsWith("FromUser:")) fromGroupUser = ExtractValue(trimmedPart, "FromUser:");
                    else if (trimmedPart.StartsWith("FromUserNickName:")) fromGroupUserNickName = ExtractValue(trimmedPart, "FromUserNickName:");
                    else if (trimmedPart.StartsWith("文本内容:"))
                    {
                        // 文本内容可能包含换行符，需要特殊处理
                        var contentIndex = message.IndexOf("文本内容:");
                        if (contentIndex >= 0)
                        {
                            var contentPart = message.Substring(contentIndex + "文本内容:".Length).Trim();
                            messageContent = contentPart;
                        }
                        break; // 找到文本内容后停止处理
                    }
                }
            }
            else
            {
                // 处理多行格式（原有逻辑）
                var lines = message.Split('\n');
                foreach (var line in lines)
                {
                    if (line.StartsWith("Wid:")) wid = ExtractValue(line, "Wid:");
                    else if (line.StartsWith("WcId:")) wcId = ExtractValue(line, "WcId:");
                    else if (line.StartsWith("FromGroup:")) fromGroup = ExtractValue(line, "FromGroup:");
                    else if (line.StartsWith("FromGroupUser:")) fromGroupUser = ExtractValue(line, "FromGroupUser:");
                    else if (line.StartsWith("FromGroupUserNickName:")) fromGroupUserNickName = ExtractValue(line, "FromGroupUserNickName:");
                    else if (line.StartsWith("FromUser:")) fromGroupUser = ExtractValue(line, "FromUser:");
                    else if (line.StartsWith("FromUserNickName:")) fromGroupUserNickName = ExtractValue(line, "FromUserNickName:");
                    else if (line.StartsWith("文本内容:")) messageContent = ExtractValue(line, "文本内容:");
                    else if (line.StartsWith("媒体内容:")) continue; // 媒体内容标题行
                    else if (line.Trim().StartsWith("1.") || line.Trim().StartsWith("2.") || line.Trim().StartsWith("3."))
                    {
                        mediaContent.Add(line.Trim());
                    }
                }
            }

            return (wid, wcId, fromGroup, fromGroupUser, fromGroupUserNickName, messageContent, mediaContent);
        }
        catch
        {
            return (null, null, null, null, null, message, new List<string>());
        }
    }

    /// <summary>
    /// 解析ChatGPT消息格式
    /// </summary>
    private (string? wid, string? wcId, string? fromGroup, string? fromGroupUser, string? fromGroupUserNickName, string? messageContent, List<string> mediaContent) ParseChatGPTMessages(dynamic messages)
    {
        try
        {
            // ChatGPT的消息在messages数组的最后一个user消息中
            foreach (var msg in messages)
            {
                if (msg.role == "user")
                {
                    var content = msg.content.ToString();
                    return ParseMaxKBMessage(content); // 使用相同的解析逻辑
                }
            }
            return (null, null, null, null, null, null, new List<string>());
        }
        catch
        {
            return (null, null, null, null, null, null, new List<string>());
        }
    }

    /// <summary>
    /// 解析Dify消息格式
    /// </summary>
    private (string? wid, string? wcId, string? fromGroup, string? fromGroupUser, string? fromGroupUserNickName, string? messageContent, List<string> mediaContent) ParseDifyQuery(string query)
    {
        return ParseMaxKBMessage(query); // 使用相同的解析逻辑
    }

    /// <summary>
    /// 解析CoZe消息格式
    /// </summary>
    private (string? wid, string? wcId, string? fromGroup, string? fromGroupUser, string? fromGroupUserNickName, string? messageContent, List<string> mediaContent) ParseCoZeMessage(string message)
    {
        return ParseMaxKBMessage(message); // 使用相同的解析逻辑
    }

    /// <summary>
    /// 从行中提取值
    /// </summary>
    private string? ExtractValue(string line, string prefix)
    {
        if (line.StartsWith(prefix))
        {
            return line.Substring(prefix.Length).Trim().TrimEnd(',');
        }
        return null;
    }
}
