using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wx;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Pipeline.Models;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Pipeline.Handlers;

namespace HappyWechat.Infrastructure.MessageQueue.Simplified.Pipeline;

/// <summary>
/// 简化消息处理管道编排器 - 完全替代MessagePipelineOrchestrator
/// 使用新的SimplifiedQueueService架构处理消息管道
/// </summary>
public class SimplifiedMessagePipelineOrchestrator : ISimplifiedMessagePipelineOrchestrator
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SimplifiedMessagePipelineOrchestrator> _logger;
    private readonly List<Type> _handlerTypes;
    private readonly ISimplifiedQueueService _queueService;

    public SimplifiedMessagePipelineOrchestrator(
        IServiceProvider serviceProvider,
        ILogger<SimplifiedMessagePipelineOrchestrator> logger,
        ISimplifiedQueueService queueService)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _queueService = queueService;
        _handlerTypes = new List<Type>
        {
            typeof(SimplifiedAccountConfigHandler),
            typeof(SimplifiedSensitiveWordHandler),
            typeof(SimplifiedFriendRequestHandler),
            typeof(SimplifiedGroupInviteHandler),
            typeof(SimplifiedMessageTypeHandler),
            // typeof(SimplifiedAiReplyHandler), // 已删除，使用新的AI处理流程
            typeof(SimplifiedFileProcessHandler)
        };
    }

    /// <summary>
    /// 处理微信回调消息
    /// </summary>
    public async Task<SimplifiedMessageProcessResult> ProcessWxCallbackMessageAsync(
        WxCallbackDto message,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证WxManagerId
            if (!message.WxManagerId.HasValue)
            {
                _logger.LogError("WxManagerId为空，无法处理消息");
                return SimplifiedMessageProcessResult.Failed("WxManagerId为空，无法处理消息");
            }

            var context = new SimplifiedMessageProcessContext
            {
                OriginalMessage = message,
                WxManagerId = message.WxManagerId.Value,
                MessageType = GetMessageTypeFromDto(message),
                ProcessingId = Guid.NewGuid().ToString("N")[..8],
                StartTime = DateTime.UtcNow,
                Properties = new Dictionary<string, object>()
            };

            _logger.LogInformation("🚀 开始处理微信回调消息 - WxManagerId: {WxManagerId}, MessageType: {MessageType}, ProcessingId: {ProcessingId}",
                context.WxManagerId, context.MessageType, context.ProcessingId);

            // 执行处理管道
            var result = await ExecutePipelineAsync(context, cancellationToken);

            var duration = (DateTime.UtcNow - context.StartTime).TotalMilliseconds;
            _logger.LogInformation("✅ 微信回调消息处理完成 - ProcessingId: {ProcessingId}, Result: {Result}, Duration: {Duration}ms",
                context.ProcessingId, result.Status, duration);

            // 记录统计信息
            await RecordStatisticsAsync(context, result, duration, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 微信回调消息处理失败 - MessageType: {MessageType}",
                message.Type);
            
            return SimplifiedMessageProcessResult.Failed($"消息处理失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 执行处理管道
    /// </summary>
    private async Task<SimplifiedMessageProcessResult> ExecutePipelineAsync(
        SimplifiedMessageProcessContext context,
        CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();

        foreach (var handlerType in _handlerTypes)
        {
            try
            {
                var handler = scope.ServiceProvider.GetService(handlerType) as ISimplifiedMessageHandler;
                if (handler == null)
                {
                    _logger.LogWarning("⚠️ 无法创建处理器实例 - HandlerType: {HandlerType}", handlerType.Name);
                    continue;
                }

                _logger.LogDebug("🔄 执行处理器 - Handler: {HandlerName}, ProcessingId: {ProcessingId}",
                    handlerType.Name, context.ProcessingId);

                var startTime = DateTime.UtcNow;
                var result = await handler.HandleAsync(context, cancellationToken);
                var processingDuration = DateTime.UtcNow - startTime;

                // 记录处理步骤
                context.ProcessingSteps.Add(new SimplifiedProcessingStep
                {
                    HandlerName = handlerType.Name,
                    Status = result.Status,
                    Message = result.Message,
                    ProcessedAt = DateTime.UtcNow,
                    Duration = processingDuration,
                    Properties = result.Properties ?? new Dictionary<string, object>()
                });

                // 检查是否需要停止处理
                if (result.ShouldStop)
                {
                    _logger.LogInformation("🛑 处理管道提前停止 - Handler: {HandlerName}, Reason: {Reason}, ProcessingId: {ProcessingId}",
                        handlerType.Name, result.Message, context.ProcessingId);
                    return result;
                }

                // 检查是否需要路由到其他队列
                if (result.ShouldRoute && !string.IsNullOrEmpty(result.NextQueue))
                {
                    await RouteToNextQueueAsync(context, result.NextQueue, result.RouteData, result.DelayMs, cancellationToken);
                }

                // 更新上下文
                if (result.UpdatedContext != null)
                {
                    context = result.UpdatedContext;
                }

                // 合并属性
                if (result.Properties != null)
                {
                    foreach (var prop in result.Properties)
                    {
                        context.Properties[prop.Key] = prop.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 处理器执行失败 - Handler: {HandlerName}, ProcessingId: {ProcessingId}",
                    handlerType.Name, context.ProcessingId);

                // 记录错误步骤
                context.ProcessingSteps.Add(new SimplifiedProcessingStep
                {
                    HandlerName = handlerType.Name,
                    Status = SimplifiedProcessStatus.Failed,
                    Message = ex.Message,
                    ProcessedAt = DateTime.UtcNow,
                    Duration = TimeSpan.Zero,
                    Properties = new Dictionary<string, object> { { "exception", ex.GetType().Name } }
                });

                // 根据错误处理策略决定是否继续
                if (ShouldStopOnError(handlerType))
                {
                    return SimplifiedMessageProcessResult.Failed($"关键处理器失败: {handlerType.Name} - {ex.Message}");
                }
            }
        }

        return SimplifiedMessageProcessResult.Success("消息处理管道执行完成");
    }

    /// <summary>
    /// 路由到下一个队列
    /// </summary>
    private async Task RouteToNextQueueAsync(
        SimplifiedMessageProcessContext context,
        string nextQueue,
        object? routeData,
        int delayMs,
        CancellationToken cancellationToken)
    {
        try
        {
            // 创建路由消息
            var routeMessage = new
            {
                OriginalMessage = context.OriginalMessage,
                ProcessingId = context.ProcessingId,
                RouteReason = "Pipeline routing",
                RoutedAt = DateTime.UtcNow,
                ProcessingSteps = context.ProcessingSteps,
                Properties = context.Properties,
                RouteData = routeData
            };

            string messageId;
            if (delayMs > 0)
            {
                messageId = await _queueService.EnqueueDelayedAsync(
                    context.WxManagerId, 
                    nextQueue, 
                    routeMessage, 
                    delayMs, 
                    priority: 0, 
                    maxRetryCount: 3, 
                    cancellationToken);
            }
            else
            {
                messageId = await _queueService.EnqueueAsync(
                    context.WxManagerId, 
                    nextQueue, 
                    routeMessage, 
                    priority: 0, 
                    maxRetryCount: 3, 
                    cancellationToken: cancellationToken);
            }

            _logger.LogInformation("📤 消息已路由到下一个队列 - Queue: {NextQueue}, ProcessingId: {ProcessingId}, MessageId: {MessageId}, DelayMs: {DelayMs}",
                nextQueue, context.ProcessingId, messageId, delayMs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 消息路由失败 - Queue: {NextQueue}, ProcessingId: {ProcessingId}",
                nextQueue, context.ProcessingId);
            throw;
        }
    }

    /// <summary>
    /// 判断是否应该在错误时停止处理
    /// </summary>
    private bool ShouldStopOnError(Type handlerType)
    {
        // 关键处理器失败时停止处理
        var criticalHandlers = new[]
        {
            typeof(SimplifiedAccountConfigHandler), // 账号配置检查是关键的
            typeof(SimplifiedMessageTypeHandler)   // 消息类型处理是关键的
        };

        return criticalHandlers.Contains(handlerType);
    }

    /// <summary>
    /// 记录统计信息
    /// </summary>
    private async Task RecordStatisticsAsync(
        SimplifiedMessageProcessContext context, 
        SimplifiedMessageProcessResult result, 
        double durationMs,
        CancellationToken cancellationToken)
    {
        try
        {
            var statsKey = $"pipeline_stats:{context.WxManagerId}:{DateTime.UtcNow:yyyy-MM-dd}";
            var stats = new
            {
                ProcessingId = context.ProcessingId,
                Status = result.Status.ToString(),
                Duration = durationMs,
                MessageType = context.MessageType,
                HandlerCount = context.ProcessingSteps.Count,
                Timestamp = DateTime.UtcNow
            };

            var statsJson = System.Text.Json.JsonSerializer.Serialize(stats);
            await _queueService.SetKeyAsync(
                $"{statsKey}:{context.ProcessingId}", 
                statsJson, 
                TimeSpan.FromDays(7), // 保留7天
                cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 记录统计信息失败 - ProcessingId: {ProcessingId}", context.ProcessingId);
        }
    }

    /// <summary>
    /// 获取处理统计信息
    /// </summary>
    public async Task<SimplifiedPipelineStatistics> GetPipelineStatisticsAsync(Guid wxManagerId, int timeRange = 24)
    {
        try
        {
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddHours(-timeRange);
            
            var stats = new SimplifiedPipelineStatistics
            {
                WxManagerId = wxManagerId,
                TimeRangeHours = timeRange,
                StartTime = startDate,
                EndTime = endDate.AddDays(1),
                TotalProcessed = 0,
                SuccessCount = 0,
                FailedCount = 0,
                AverageProcessingTimeMs = 0,
                HandlerStatistics = new Dictionary<string, SimplifiedHandlerStatistics>(),
                LastUpdated = DateTime.UtcNow
            };

            // 从Redis获取统计数据
            var database = _queueService.GetDatabase();
            var keyPattern = $"pipeline_stats:{wxManagerId}:*";
            
            // 这里可以实现更复杂的统计逻辑
            // 暂时返回基础统计信息
            
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取管道统计信息失败 - WxManagerId: {WxManagerId}", wxManagerId);
            throw;
        }
    }

    /// <summary>
    /// 从DTO获取消息类型
    /// </summary>
    private int GetMessageTypeFromDto(WxCallbackDto message)
    {
        return message.Type?.ToLower() switch
        {
            "text" => 1,
            "image" => 3,
            "voice" => 34,
            "video" => 43,
            "file" => 49,
            "friend_request" => 37,
            "group_invite" => 10000,
            _ => 1 // 默认为文本消息
        };
    }
}

/// <summary>
/// 简化消息处理管道编排器接口
/// </summary>
public interface ISimplifiedMessagePipelineOrchestrator
{
    Task<SimplifiedMessageProcessResult> ProcessWxCallbackMessageAsync(
        WxCallbackDto message,
        CancellationToken cancellationToken = default);

    Task<SimplifiedPipelineStatistics> GetPipelineStatisticsAsync(Guid wxManagerId, int timeRange = 24);
}