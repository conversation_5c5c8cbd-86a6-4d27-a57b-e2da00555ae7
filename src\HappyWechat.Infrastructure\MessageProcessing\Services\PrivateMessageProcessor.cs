using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.AI;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.Constants;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.RiskControl.Services;
using HappyWechat.Infrastructure.MediaProcessing;
using HappyWechat.Infrastructure.MediaProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using HappyWechat.Domain.ValueObjects;
using HappyWechat.Domain.ValueObjects.Enums;
using Microsoft.EntityFrameworkCore;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 私聊消息处理器 - 实现完整的私聊消息处理流程
/// 包含AI配置检查、AI处理和消息发送
/// </summary>
public interface IPrivateMessageProcessor
{
    /// <summary>
    /// 处理私聊消息
    /// </summary>
    Task<PrivateMessageProcessResult> ProcessPrivateMessageAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 私聊消息处理结果
/// </summary>
public class PrivateMessageProcessResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ProcessingDetails { get; set; }
    public bool ShouldContinue { get; set; } = true;
    public ContactAiConfigResult? ConfigResult { get; set; }

    public static PrivateMessageProcessResult CreateSuccess(string details, ContactAiConfigResult? configResult = null)
    {
        return new PrivateMessageProcessResult
        {
            Success = true,
            ShouldContinue = true,
            ProcessingDetails = details,
            ConfigResult = configResult
        };
    }

    public static PrivateMessageProcessResult CreateFailure(string errorMessage, ContactAiConfigResult? configResult = null)
    {
        return new PrivateMessageProcessResult
        {
            Success = false,
            ShouldContinue = false,
            ErrorMessage = errorMessage,
            ConfigResult = configResult
        };
    }

    public static PrivateMessageProcessResult CreateSkipped(string reason, ContactAiConfigResult? configResult = null)
    {
        return new PrivateMessageProcessResult
        {
            Success = true,
            ShouldContinue = false,
            ProcessingDetails = reason,
            ConfigResult = configResult
        };
    }
}

/// <summary>
/// 私聊消息处理器实现
/// </summary>
public class PrivateMessageProcessor : IPrivateMessageProcessor
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IContactAiConfigChecker _contactAiConfigChecker;
    private readonly IAiService _aiService;
    private readonly IWxMessageService _wxMessageService;
    private readonly IServiceProvider _serviceProvider;
    private readonly IUnifiedMediaProcessor _mediaProcessor;
    private readonly ILogger<PrivateMessageProcessor> _logger;

    public PrivateMessageProcessor(
        ApplicationDbContext dbContext,
        IContactAiConfigChecker contactAiConfigChecker,
        IAiService aiService,
        IWxMessageService wxMessageService,
        IServiceProvider serviceProvider,
        IUnifiedMediaProcessor mediaProcessor,
        ILogger<PrivateMessageProcessor> logger)
    {
        _dbContext = dbContext;
        _contactAiConfigChecker = contactAiConfigChecker;
        _aiService = aiService;
        _wxMessageService = wxMessageService;
        _serviceProvider = serviceProvider;
        _mediaProcessor = mediaProcessor;
        _logger = logger;
    }

    public async Task<PrivateMessageProcessResult> ProcessPrivateMessageAsync(
        WxCallbackMessageDto callbackMessage, 
        string processingId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var fromUser = callbackMessage.Data?.FromUser;

            _logger.LogInformation("[{ProcessingId}] 👤 开始处理私聊消息 - FromUser: {FromUser}, MessageType: {MessageType}",
                processingId, fromUser, callbackMessage.MessageType);

            // 1. 基础验证
            if (string.IsNullOrEmpty(fromUser))
            {
                return PrivateMessageProcessResult.CreateFailure("FromUser为空");
            }

            // 2. 联系人AI配置检查
            var configResult = await _contactAiConfigChecker.CheckContactAiConfigAsync(wxManagerId, fromUser);
            
            if (!configResult.ContactExists)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 联系人不存在，消息被丢弃 - FromUser: '{FromUser}'",
                    processingId, fromUser);
                return PrivateMessageProcessResult.CreateSkipped("联系人不存在", configResult);
            }

            if (!configResult.IsAiConfigured)
            {
                _logger.LogDebug("[{ProcessingId}] 🚫 联系人AI未配置，跳过处理 - FromUser: {FromUser}, ContactName: {ContactName}",
                    processingId, fromUser, configResult.ContactName);
                return PrivateMessageProcessResult.CreateSkipped("联系人AI未配置", configResult);
            }

            // 3. 媒体处理（如果需要）
            MediaProcessingResult? mediaResult = null;
            if (ShouldProcessMedia(callbackMessage.MessageType!))
            {
                mediaResult = await _mediaProcessor.ProcessMediaMessageAsync(callbackMessage, cancellationToken);
                if (mediaResult != null && !mediaResult.Success)
                {
                    _logger.LogWarning("[{ProcessingId}] ⚠️ 媒体处理失败 - Error: {Error}",
                        processingId, mediaResult.ErrorMessage);
                }
            }

            // 4. AI处理，无需@逻辑（私聊模式）
            var aiResult = await ProcessAiReplyAsync(callbackMessage, configResult, mediaResult, processingId, cancellationToken);
            
            if (!aiResult.Success)
            {
                return PrivateMessageProcessResult.CreateFailure(aiResult.ErrorMessage!, configResult);
            }

            // 5. 消息发送
            var sendResult = await SendPrivateReplyAsync(callbackMessage, aiResult.AiResponse!, processingId, cancellationToken);
            
            if (sendResult.Success)
            {
                _logger.LogInformation("[{ProcessingId}] ✅ 私聊消息处理完成 - FromUser: {FromUser}, ContactName: {ContactName}",
                    processingId, fromUser, configResult.ContactName);
                return PrivateMessageProcessResult.CreateSuccess("私聊消息处理完成", configResult);
            }
            else
            {
                return PrivateMessageProcessResult.CreateFailure($"消息发送失败: {sendResult.ErrorMessage}", configResult);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 私聊消息处理异常", processingId);
            return PrivateMessageProcessResult.CreateFailure($"处理异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理AI回复
    /// </summary>
    private async Task<(bool Success, string? ErrorMessage, AiMessageDto? AiResponse)> ProcessAiReplyAsync(
        WxCallbackMessageDto callbackMessage,
        ContactAiConfigResult configResult,
        MediaProcessingResult? mediaResult,
        string processingId,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🤖 开始AI处理 - ContactName: {ContactName}, AiAgentId: {AiAgentId}",
                processingId, configResult.ContactName, configResult.AiAgentId);

            // 获取WxManager信息用于AI传参
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var wxManager = await _dbContext.WxManagerEntities
                .Where(w => w.Id == wxManagerId)
                .Select(w => new { w.WId, w.WcId })
                .FirstOrDefaultAsync(cancellationToken);

            // 构建AI消息DTO，携带完整的消息参数（包括媒体信息）
            var processStartTime = DateTime.UtcNow;
            var aiMessageDto = new AiMessageDto
            {
                WxManagerId = wxManagerId,
                Content = callbackMessage.Data?.Content,
                MessageType = callbackMessage.MessageType,
                FromUser = callbackMessage.Data?.FromUser,
                ToUser = callbackMessage.Data?.ToUser,
                IsGroupMessage = false, // 私聊消息
                Context = new Dictionary<string, object>
                {
                    // 基础配置信息
                    ["ContactName"] = configResult.ContactName!,
                    ["AiAgentId"] = configResult.AiAgentId!,
                    ["ProcessingId"] = processingId,

                    // 微信消息完整参数 - 私聊模式
                    ["WId"] = wxManager?.WId ?? "",
                    ["WcId"] = callbackMessage.Data?.FromUser ?? "", // 私聊中WcId就是发送者ID
                    ["Content"] = callbackMessage.Data?.Content ?? "",
                    ["NickName"] = configResult.ContactName!, // 私聊联系人昵称
                    ["Timestamp"] = DateTime.UtcNow,
                    ["ProcessStartTime"] = processStartTime,
                    ["ProviderType"] = "待确定", // 将在AI服务中更新

                    // 媒体信息（如果有）
                    ["MediaUrls"] = mediaResult?.PublicUrl != null ? new List<string> { mediaResult.PublicUrl } : new List<string>(),
                    ["MediaDescriptions"] = mediaResult?.FileName != null ? new List<string> { $"文件: {mediaResult.FileName}" } : new List<string>(),
                    ["HasMedia"] = mediaResult?.Success == true,

                    // 原始回调数据
                    ["OriginalCallback"] = callbackMessage
                }
            };

            // 检查ChatGPT媒体消息跳过逻辑
            var aiAgentService = _serviceProvider.GetRequiredService<IAiAgentService>();
            var agentInfo = await aiAgentService.GetByIdAsync(configResult.AiAgentId!.Value);

            if (agentInfo?.ProviderType == AiProviderType.ChatGPT &&
                !AiMessageTemplate.IsTextMessage(callbackMessage.MessageType))
            {
                _logger.LogWarning("[{ProcessingId}] ChatGPT不支持处理媒体消息，消息类型：{MessageType}，跳过处理 - ContactName: {ContactName}",
                    processingId, callbackMessage.MessageType, configResult.ContactName);
                return (false, $"ChatGPT不支持处理媒体消息，消息类型：{callbackMessage.MessageType}", null);
            }

            // 调用AI服务获取原始响应
            var aiResponse = await aiAgentService.ProcessMessageAsync(
                configResult.AiAgentId!.Value,
                callbackMessage.Data?.Content ?? "",
                aiMessageDto);

            if (string.IsNullOrWhiteSpace(aiResponse))
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ AI处理返回空结果 - ContactName: {ContactName}",
                    processingId, configResult.ContactName);
                return (false, "AI处理返回空结果", null);
            }

            // 使用AiResponseProcessor处理AI响应（包括markdown清理和图文混排解析）
            var responseProcessor = _serviceProvider.GetRequiredService<IAiResponseProcessor>();
            var processedResult = await responseProcessor.ProcessAiResponseAsync(
                aiResponse,
                Guid.Parse(callbackMessage.WxManagerId!),
                callbackMessage.Data?.WId ?? "",
                callbackMessage.Data?.FromUser ?? "");

            // 敏感词检测
            var sensitiveWordService = _serviceProvider.GetRequiredService<SensitiveWordDetectionService>();
            var sensitiveResult = await sensitiveWordService.ProcessAIReplyAsync(aiResponse);

            if (sensitiveResult.ShouldBlock)
            {
                _logger.LogWarning("[{ProcessingId}] AI回复被敏感词拦截 - ContactName: {ContactName}",
                    processingId, configResult.ContactName);
                return (false, "AI回复包含敏感词，已拦截", null);
            }

            // 如果敏感词被替换，使用替换后的内容
            var finalContent = sensitiveResult.ProcessedText;

            _logger.LogDebug("[{ProcessingId}] ✅ AI处理成功 - ContactName: {ContactName}, ResponseLength: {Length}",
                processingId, configResult.ContactName, finalContent.Length);

            // 构建响应DTO
            var response = new AiMessageDto
            {
                WxManagerId = Guid.Parse(callbackMessage.WxManagerId!),
                Content = finalContent,
                MessageType = "text",
                FromUser = "AI",
                ToUser = callbackMessage.Data?.FromUser,
                IsGroupMessage = false,
                CreatedAt = DateTime.UtcNow
            };

            return (true, null, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ AI处理异常 - ContactName: {ContactName}",
                processingId, configResult.ContactName);
            return (false, $"AI处理异常: {ex.Message}", null);
        }
    }

    /// <summary>
    /// 发送私聊回复
    /// </summary>
    private async Task<(bool Success, string? ErrorMessage)> SendPrivateReplyAsync(
        WxCallbackMessageDto callbackMessage,
        AiMessageDto aiResponse,
        string processingId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 获取WxManager信息
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);
            var wxManager = await _dbContext.WxManagerEntities
                .Where(w => w.Id == wxManagerId)
                .Select(w => new { w.WId })
                .FirstOrDefaultAsync(cancellationToken);

            if (wxManager == null)
            {
                return (false, $"未找到WxManager - WxManagerId: {wxManagerId}");
            }

            // 构建发送命令
            var sendCommand = new WxSendTextMessageCommand
            {
                WId = wxManager.WId,
                WcId = callbackMessage.Data?.FromUser!, // 私聊回复给发送者
                Content = aiResponse.Content!
            };

            // 发送消息
            var result = await _wxMessageService.SendTextMessageAsync(Guid.Empty, sendCommand);

            if (result.IsSuccess)
            {
                _logger.LogInformation("[{ProcessingId}] 🚀 发送wx文本消息成功 - WId: {WId}, WcId: {WcId}, Content: {Content}",
                    processingId, sendCommand.WId, sendCommand.WcId, 
                    sendCommand.Content?.Substring(0, Math.Min(50, sendCommand.Content?.Length ?? 0)));
                return (true, null);
            }
            else
            {
                _logger.LogError("[{ProcessingId}] ❌ 发送wx文本消息失败 - Error: {Error}",
                    processingId, result.Message);
                return (false, result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 发送私聊回复异常", processingId);
            return (false, $"发送异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 判断是否需要处理媒体文件
    /// </summary>
    private bool ShouldProcessMedia(string messageType)
    {
        return messageType switch
        {
            "60002" => true, // 私聊图片
            "60004" => true, // 私聊语音
            "60009" => true, // 私聊文件
            _ => false
        };
    }
}