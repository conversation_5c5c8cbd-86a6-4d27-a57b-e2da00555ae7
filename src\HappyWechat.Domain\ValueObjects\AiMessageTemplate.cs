namespace HappyWechat.Domain.ValueObjects;

/// <summary>
/// AI消息模板配置
/// </summary>
public class AiMessageTemplate
{
    /// <summary>
    /// 私聊文本消息模板
    /// </summary>
    public const string PRIVATE_TEXT_TEMPLATE = @"=== 消息基础信息 ===
微信管理器ID: {WxManagerId}
机器人WId: {WId}
机器人WcId: {WcId}
消息类型: {MessageTypeDescription}
时间戳: {Timestamp}

=== 发送者信息 ===
发送者WcId: {FromUser}
发送者昵称: {FromUserNickName}

=== 消息内容 ===
{Content}

=== 技术信息 ===
AI提供商: {ProviderType}
处理时间: {ProcessTime}
";

    /// <summary>
    /// 私聊媒体消息模板
    /// </summary>
    public const string PRIVATE_MEDIA_TEMPLATE = @"=== 消息基础信息 ===
微信管理器ID: {WxManagerId}
机器人WId: {WId}
机器人WcId: {WcId}
消息类型: {MessageTypeDescription}
时间戳: {Timestamp}

=== 发送者信息 ===
发送者WcId: {FromUser}
发送者昵称: {FromUserNickName}

=== 消息内容 ===
{MediaContent}

=== 技术信息 ===
AI提供商: {ProviderType}
处理时间: {ProcessTime}

请分析以上{MediaType}内容并给出相应的回复。
";

    /// <summary>
    /// 群聊文本消息模板
    /// </summary>
    public const string GROUP_TEXT_TEMPLATE = @"=== 消息基础信息 ===
微信管理器ID: {WxManagerId}
机器人WId: {WId}
机器人WcId: {WcId}
消息类型: {MessageTypeDescription}
时间戳: {Timestamp}

=== 发送者信息 ===
发送者WcId: {FromUser}
发送者昵称: {FromUserNickName}

=== 群聊信息 ===
群组ID: {FromGroup}
群内发送者: {FromGroupUser}
群内发送者昵称: {FromGroupUserNickName}
@用户列表: [{AtList}]

=== 消息内容 ===
{Content}

=== 技术信息 ===
AI提供商: {ProviderType}
处理时间: {ProcessTime}
";

    /// <summary>
    /// 群聊媒体消息模板
    /// </summary>
    public const string GROUP_MEDIA_TEMPLATE = @"=== 消息基础信息 ===
微信管理器ID: {WxManagerId}
机器人WId: {WId}
机器人WcId: {WcId}
消息类型: {MessageTypeDescription}
时间戳: {Timestamp}

=== 发送者信息 ===
发送者WcId: {FromUser}
发送者昵称: {FromUserNickName}

=== 群聊信息 ===
群组ID: {FromGroup}
群内发送者: {FromGroupUser}
群内发送者昵称: {FromGroupUserNickName}
@用户列表: [{AtList}]

=== 消息内容 ===
{MediaContent}

=== 技术信息 ===
AI提供商: {ProviderType}
处理时间: {ProcessTime}

请分析以上{MediaType}内容并给出相应的回复。
";
}

/// <summary>
/// AI提供商类型枚举 (用于模板选择)
/// </summary>
public enum AiProviderTemplateType
{
    /// <summary>
    /// 使用模板格式 (Coze, Dify, MaxKB)
    /// </summary>
    Templated,
    
    /// <summary>
    /// 使用原始格式 (ChatGPT)
    /// </summary>
    Original
}

/// <summary>
/// 媒体类型描述映射
/// </summary>
public static class MediaTypeDescriptions
{
    public static readonly Dictionary<string, string> TypeMap = new()
    {
        { "60002", "图片" },      // 私聊图片
        { "60003", "视频" },      // 私聊视频
        { "60004", "语音" },      // 私聊语音
        { "60009", "文件" },      // 私聊文件发送完成
        { "80002", "图片" },      // 群聊图片
        { "80003", "视频" },      // 群聊视频
        { "80004", "语音" },      // 群聊语音
        { "80009", "文件" }       // 群聊文件发送完成
    };
    
    /// <summary>
    /// 获取媒体类型描述
    /// </summary>
    public static string GetDescription(string messageType)
    {
        return TypeMap.TryGetValue(messageType, out var description) ? description : "媒体";
    }
}
