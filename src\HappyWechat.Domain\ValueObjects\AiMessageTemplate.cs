namespace HappyWechat.Domain.ValueObjects;

/// <summary>
/// AI消息模板配置 - 简化版本
/// </summary>
public class AiMessageTemplate
{
    /// <summary>
    /// 私聊文本消息模板
    /// </summary>
    public const string PRIVATE_TEXT_TEMPLATE = "WId: {WId},WcId: {WcId},FromUserWcId: {FromUser},FromUserNickName: {FromUserNickName}，文本消息内容：{Content}";

    /// <summary>
    /// 私聊语音消息模板
    /// </summary>
    public const string PRIVATE_VOICE_TEMPLATE = "WId: {WId},WcId: {WcId},FromUserWcId: {FromUser},FromUserNickName: {FromUserNickName}，语音文件链接: {MediaUrl}，请分析以上语音内容并给出相应的回复。";

    /// <summary>
    /// 私聊图片消息模板
    /// </summary>
    public const string PRIVATE_IMAGE_TEMPLATE = "WId: {WId},WcId: {WcId},FromUserWcId: {FromUser},FromUserNickName: {FromUserNickName}，图片文件链接: {MediaUrl}，请分析以上图片内容并给出相应的回复。";

    /// <summary>
    /// 私聊文件消息模板
    /// </summary>
    public const string PRIVATE_FILE_TEMPLATE = "WId: {WId},WcId: {WcId},FromUserWcId: {FromUser},FromUserNickName: {FromUserNickName}，文件链接: {MediaUrl}，请分析以上文件内容并给出相应的回复。";

    /// <summary>
    /// 群聊文本消息模板
    /// </summary>
    public const string GROUP_TEXT_TEMPLATE = "WId: {WId},WcId: {WcId},FromGroup: {FromGroup},FromGroupUser: {FromGroupUser}，文本消息内容：{Content}";

    /// <summary>
    /// 群聊语音消息模板
    /// </summary>
    public const string GROUP_VOICE_TEMPLATE = "WId: {WId},WcId: {WcId},FromGroup: {FromGroup},FromGroupUser: {FromGroupUser}，语音文件链接: {MediaUrl}，请分析以上语音内容并给出相应的回复。";

    /// <summary>
    /// 群聊图片消息模板
    /// </summary>
    public const string GROUP_IMAGE_TEMPLATE = "WId: {WId},WcId: {WcId},FromGroup: {FromGroup},FromGroupUser: {FromGroupUser}，图片文件链接: {MediaUrl}，请分析以上图片内容并给出相应的回复。";

    /// <summary>
    /// 群聊文件消息模板
    /// </summary>
    public const string GROUP_FILE_TEMPLATE = "WId: {WId},WcId: {WcId},FromGroup: {FromGroup},FromGroupUser: {FromGroupUser}，文件链接: {MediaUrl}，请分析以上文件内容并给出相应的回复。";

    /// <summary>
    /// 根据消息类型获取对应的模板
    /// </summary>
    public static string GetTemplate(string messageType, bool isGroupMessage)
    {
        return messageType switch
        {
            "60001" => PRIVATE_TEXT_TEMPLATE,      // 私聊文本
            "60002" => PRIVATE_IMAGE_TEMPLATE,     // 私聊图片
            "60004" => PRIVATE_VOICE_TEMPLATE,     // 私聊语音
            "60009" => PRIVATE_FILE_TEMPLATE,      // 私聊文件
            "80001" => GROUP_TEXT_TEMPLATE,        // 群聊文本
            "80002" => GROUP_IMAGE_TEMPLATE,       // 群聊图片
            "80004" => GROUP_VOICE_TEMPLATE,       // 群聊语音
            "80009" => GROUP_FILE_TEMPLATE,        // 群聊文件
            _ => isGroupMessage ? GROUP_TEXT_TEMPLATE : PRIVATE_TEXT_TEMPLATE
        };
    }

    /// <summary>
    /// 判断是否为文本消息
    /// </summary>
    public static bool IsTextMessage(string messageType)
    {
        return messageType == "60001" || messageType == "80001";
    }

    /// <summary>
    /// 判断是否为媒体消息
    /// </summary>
    public static bool IsMediaMessage(string messageType)
    {
        return messageType is "60002" or "60004" or "60009" or "80002" or "80004" or "80009";
    }
}



/// <summary>
/// 媒体类型描述映射
/// </summary>
public static class MediaTypeDescriptions
{
    public static readonly Dictionary<string, string> TypeMap = new()
    {
        { "60002", "图片" },      // 私聊图片
        { "60003", "视频" },      // 私聊视频
        { "60004", "语音" },      // 私聊语音
        { "60009", "文件" },      // 私聊文件发送完成
        { "80002", "图片" },      // 群聊图片
        { "80003", "视频" },      // 群聊视频
        { "80004", "语音" },      // 群聊语音
        { "80009", "文件" }       // 群聊文件发送完成
    };
    
    /// <summary>
    /// 获取媒体类型描述
    /// </summary>
    public static string GetDescription(string messageType)
    {
        return TypeMap.TryGetValue(messageType, out var description) ? description : "媒体";
    }
}
