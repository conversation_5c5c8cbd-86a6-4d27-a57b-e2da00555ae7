using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Constants;
using HappyWechat.Infrastructure.Identity.Repositories;
using Microsoft.EntityFrameworkCore;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 增强的群消息处理器 - 按消息类型区分@判断规则
/// </summary>
public interface IEnhancedGroupMessageProcessor
{
    /// <summary>
    /// 检查群消息是否应该触发AI回复
    /// </summary>
    Task<GroupMessageProcessResult> ShouldTriggerAiReplyAsync(WxCallbackMessageDto callbackMessage);
}

/// <summary>
/// 群消息处理结果
/// </summary>
public class GroupMessageProcessResult
{
    public bool ShouldReply { get; set; }
    public string Reason { get; set; } = string.Empty;
    public bool IsConfigured { get; set; }
    public bool IsMentioned { get; set; }
    public string MessageType { get; set; } = string.Empty;
    public string GroupName { get; set; } = string.Empty;
}

/// <summary>
/// 增强的群消息处理器实现
/// </summary>
public class EnhancedGroupMessageProcessor : IEnhancedGroupMessageProcessor
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<EnhancedGroupMessageProcessor> _logger;

    public EnhancedGroupMessageProcessor(
        ApplicationDbContext dbContext,
        ILogger<EnhancedGroupMessageProcessor> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<GroupMessageProcessResult> ShouldTriggerAiReplyAsync(WxCallbackMessageDto callbackMessage)
    {
        try
        {
            var messageType = callbackMessage.MessageType;
            var fromGroup = callbackMessage.Data?.FromGroup;
            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId!);

            _logger.LogDebug("检查群消息AI回复触发条件 - MessageType: {MessageType}, Group: {Group}", 
                messageType, fromGroup);

            // 1. 基础验证
            if (string.IsNullOrEmpty(fromGroup))
            {
                return new GroupMessageProcessResult
                {
                    ShouldReply = false,
                    Reason = "群组信息缺失",
                    MessageType = messageType!
                };
            }

            // 2. 查询群组配置
            var groupConfig = await _dbContext.WxGroupEntities
                .Where(g => g.WxManagerId == wxManagerId && g.ChatRoomId == fromGroup)
                .Select(g => new
                {
                    g.NickName,
                    g.IsAiEnabled,
                    g.OnlyReplyWhenMentioned,
                    g.AiAgentId
                })
                .FirstOrDefaultAsync();

            if (groupConfig == null)
            {
                return new GroupMessageProcessResult
                {
                    ShouldReply = false,
                    Reason = "群组不存在",
                    MessageType = messageType!,
                    IsConfigured = false
                };
            }

            // 3. 检查AI配置
            if (!groupConfig.IsAiEnabled || !groupConfig.AiAgentId.HasValue)
            {
                return new GroupMessageProcessResult
                {
                    ShouldReply = false,
                    Reason = "群组AI未启用或未配置代理",
                    MessageType = messageType!,
                    IsConfigured = false,
                    GroupName = groupConfig.NickName ?? fromGroup
                };
            }

            // 4. 按消息类型应用不同的@判断规则
            var shouldReply = await ApplyMessageTypeRulesAsync(messageType!, groupConfig.OnlyReplyWhenMentioned, callbackMessage);
            var isMentioned = IsMessageMentioned(callbackMessage);

            _logger.LogDebug("群消息处理结果 - MessageType: {MessageType}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, IsMentioned: {IsMentioned}, ShouldReply: {ShouldReply}",
                messageType, groupConfig.OnlyReplyWhenMentioned, isMentioned, shouldReply);

            return new GroupMessageProcessResult
            {
                ShouldReply = shouldReply,
                Reason = shouldReply ? "满足回复条件" : GetRejectReason(messageType!, groupConfig.OnlyReplyWhenMentioned, isMentioned),
                IsConfigured = true,
                IsMentioned = isMentioned,
                MessageType = messageType!,
                GroupName = groupConfig.NickName ?? fromGroup
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查群消息AI回复触发条件异常");
            return new GroupMessageProcessResult
            {
                ShouldReply = false,
                Reason = $"处理异常: {ex.Message}",
                MessageType = callbackMessage.MessageType!
            };
        }
    }

    /// <summary>
    /// 按消息类型应用不同的@判断规则
    /// </summary>
    private async Task<bool> ApplyMessageTypeRulesAsync(string messageType, bool onlyReplyWhenMentioned, WxCallbackMessageDto callbackMessage)
    {
        // 根据需求文档的规则：
        // - 80001(群聊文本)和80004(群聊语音)：需要判断OnlyReplyWhenMentioned
        // - 80008(群聊文件)和80002(群聊图片)：全部放行，只需要判断AI是否模式是自动回复，且机器人id不为空

        return messageType switch
        {
            MessageTypeConstants.GROUP_TEXT or MessageTypeConstants.GROUP_VOICE => 
                await ApplyMentionRuleAsync(onlyReplyWhenMentioned, callbackMessage),
            
            MessageTypeConstants.GROUP_FILE or MessageTypeConstants.GROUP_IMAGE => 
                await ApplyAutoReplyRuleAsync(callbackMessage),
            
            // 其他群消息类型默认应用@规则
            _ when MessageTypeConstants.IsGroupMessage(messageType) => 
                await ApplyMentionRuleAsync(onlyReplyWhenMentioned, callbackMessage),
            
            _ => false
        };
    }

    /// <summary>
    /// 应用@提及规则（80001和80004）
    /// </summary>
    private async Task<bool> ApplyMentionRuleAsync(bool onlyReplyWhenMentioned, WxCallbackMessageDto callbackMessage)
    {
        if (!onlyReplyWhenMentioned)
        {
            // 配置为所有消息回复
            return true;
        }

        // 配置为仅@后回复，检查是否被@
        var isMentioned = IsMessageMentioned(callbackMessage);
        
        _logger.LogDebug("应用@提及规则 - OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, IsMentioned: {IsMentioned}",
            onlyReplyWhenMentioned, isMentioned);

        return isMentioned;
    }

    /// <summary>
    /// 🔧 重构：应用自动回复规则（80009和80002）- 使用80009替代80008
    /// </summary>
    private async Task<bool> ApplyAutoReplyRuleAsync(WxCallbackMessageDto callbackMessage)
    {
        // 对于文件和图片消息，只要AI模式是自动回复且机器人ID不为空就放行
        // 这里的逻辑是：如果群组配置了AI且有代理ID，就认为满足条件
        // （在调用此方法前已经验证过IsAiEnabled和AiAgentId）
        
        _logger.LogDebug("应用自动回复规则 - MessageType: {MessageType}, 直接放行", 
            callbackMessage.MessageType);

        return true;
    }

    /// <summary>
    /// 检查消息是否@了机器人
    /// </summary>
    private bool IsMessageMentioned(WxCallbackMessageDto callbackMessage)
    {
        try
        {
            // 🔧 修复：使用正确的机器人ID获取逻辑
            var botWcId = callbackMessage.WcId; // 直接使用消息中的WcId作为机器人ID
            var atList = callbackMessage.Data?.Atlist ?? new List<string>();
            var content = callbackMessage.Data?.Content ?? "";

            _logger.LogDebug("🔍 开始@检测 - BotWcId: {BotWcId}, AtList: [{AtList}], Content: '{Content}'",
                botWcId, string.Join(",", atList), content.Length > 50 ? content.Substring(0, 50) + "..." : content);

            if (string.IsNullOrEmpty(botWcId))
            {
                _logger.LogWarning("⚠️ 机器人wcId为空，无法判断@状态");
                return false;
            }

            // 🔧 增强@检测逻辑：支持多种@检测方式
            var isMentioned = false;

            // 方式1：检查@列表中是否包含机器人WcId
            if (atList.Contains(botWcId))
            {
                isMentioned = true;
                _logger.LogDebug("✅ @检测成功 - 方式1：@列表包含机器人WcId");
            }

            // 🔧 删除硬编码的特殊目标检测，统一使用动态机器人ID
            // 方式2：检查消息内容是否包含@所有人
            if (!isMentioned && content.Contains("@所有人"))
            {
                isMentioned = true;
                _logger.LogDebug("✅ @检测成功 - 方式2：消息内容包含@所有人");
            }

            _logger.LogDebug("🎯 @状态检查完成 - BotWcId: {BotWcId}, AtList: [{AtList}], IsMentioned: {IsMentioned}",
                botWcId, string.Join(",", atList), isMentioned);

            return isMentioned;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 检查@状态异常");
            return false;
        }
    }

    /// <summary>
    /// 获取拒绝回复的原因
    /// </summary>
    private string GetRejectReason(string messageType, bool onlyReplyWhenMentioned, bool isMentioned)
    {
        return messageType switch
        {
            MessageTypeConstants.GROUP_TEXT or MessageTypeConstants.GROUP_VOICE when onlyReplyWhenMentioned && !isMentioned =>
                "群聊文本/语音消息需要@机器人才能触发回复",
            
            MessageTypeConstants.GROUP_TEXT or MessageTypeConstants.GROUP_VOICE when !onlyReplyWhenMentioned =>
                "群聊文本/语音消息配置为所有消息回复，应该触发",
            
            MessageTypeConstants.GROUP_FILE or MessageTypeConstants.GROUP_IMAGE =>
                "群聊文件/图片消息应该自动回复，检查AI配置",
            
            _ => "未知的拒绝原因"
        };
    }
}
